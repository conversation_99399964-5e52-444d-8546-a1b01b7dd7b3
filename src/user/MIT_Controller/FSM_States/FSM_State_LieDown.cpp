// FSM_State_LieDown.cpp
#include "FSM_State_LieDown.h"

/**
 * Constructor for the FSM State that passes in state specific info to
 * the generic FSM State constructor.
 *
 * @param _controlFSMData holds all of the relevant control data
 */
template <typename T>
FSM_State_LieDown<T>::FSM_State_LieDown(ControlFSMData<T>* _controlFSMData)
    : FSM_State<T>(_controlFSMData, FSM_StateName::LIE_DOWN, "LIE_DOWN"){
  // Do nothing
  // Set the pre controls safety checks
  this->checkSafeOrientation = false;

  // Post control safety checks
  this->checkPDesFoot = false;
  this->checkForceFeedForward = false;

  zero_vec3.setZero();

  fold_jpos[0] << -0.22f, -1.17f, 2.7f;
  fold_jpos[1] << 0.22f, -1.17f, 2.7f;
  fold_jpos[2] << -0.22f, -1.17f, 2.7f;
  fold_jpos[3] << 0.22f, -1.17f, 2.7f;
}

template <typename T>
void FSM_State_LieDown<T>::onEnter() {
  // Default is to not transition
  this->nextStateName = this->stateName;

  // Reset the transition data
  this->transitionData.zero();

  // Reset iteration counter
  curr_iter = 0;

  for(size_t leg(0); leg<4; ++leg){
    initial_jpos[leg] = this->_data->_legController->datas[leg].q;
  }
}

/**
 * Calls the functions to be executed on each control loop iteration.
 */
template <typename T>
void FSM_State_LieDown<T>::run() {

 if (curr_iter == 0) {
    for (size_t i = 0; i < 4; ++i) {
      initial_jpos[i] = this->_data->_legController->datas[i].q;
    }
  }

  if (curr_iter <= 500) {
    for(size_t leg(0); leg<4; ++leg){

      float a(0.f);
      float b(1.f);

      if(curr_iter <= 500) {
        b = (float)curr_iter/500;
        a = 1.f - b;
      }

      Vec3<T> inter_pos = a * initial_jpos[leg] + b * fold_jpos[leg]; 

      this->jointPDControl(leg, inter_pos, zero_vec3);
    }

  } else {
      // 到达目标位置，保持趴下状态
      for (size_t leg = 0; leg < 4; ++leg) {
        this->_data->_legController->commands[leg].qDes = fold_jpos[leg];
      }
    }
 ++curr_iter;
}

/**
 * Manages which states can be transitioned into either by the user
 * commands or state event triggers.
 *
 * @return the enumerated FSM state name to transition into
 */
template <typename T>
FSM_StateName FSM_State_LieDown<T>::checkTransition() {
  this->nextStateName = this->stateName;

  // Switch FSM control mode
  switch ((int)this->_data->controlParameters->control_mode) {
    case K_LIE_DOWN:
      break;
    case K_RECOVERY_STAND:
      // Requested switch to joint PD control
      this->nextStateName = FSM_StateName::RECOVERY_STAND;
      break;
    case K_PASSIVE:  // normal c
      this->nextStateName = FSM_StateName::PASSIVE;
      break;

    default:
      std::cout << "[CONTROL FSM] Bad Request: Cannot transition from "
                << K_PASSIVE << " to "
                << this->_data->controlParameters->control_mode << std::endl;
  }

  // Get the next state
  return this->nextStateName;
}

/**
 * Handles the actual transition for the robot between states.
 * Returns true when the transition is completed.
 *
 * @return true if transition is complete
 */
template <typename T>
TransitionData<T> FSM_State_LieDown<T>::transition() {
  // Finish Transition
  switch (this->nextStateName) {
    case FSM_StateName::PASSIVE:  // normal
      this->transitionData.done = true;
      break;

    case FSM_StateName::RECOVERY_STAND:
      this->transitionData.done = true;
      break;

    default:
      std::cout << "[CONTROL FSM] Something went wrong in transition"
                << std::endl;
  }

  // Return the transition data to the FSM
  return this->transitionData;
}

/**
 * Cleans up the state information on exiting the state.
 */
template <typename T>
void FSM_State_LieDown<T>::onExit() {
  // Nothing to clean up when exiting
}

template class FSM_State_LieDown<float>;