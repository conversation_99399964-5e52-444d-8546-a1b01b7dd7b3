import lcm
import matplotlib.pyplot as plt
import numpy as np
import time
import csv
from datetime import datetime
import sys
import os
import signal

# 导入LCM类型
sys.path.append("/home/<USER>/Desktop/03_mitcontrol_latest/src/lcm-types")
try:
    from wbc_test_data_t import wbc_test_data_t
except ImportError:
    print("Error: Could not import wbc_test_data_t. Make sure the LCM type is generated.")
    sys.exit(1)

class DataStorage:
    def __init__(self):
        self.timestamps = []
        self.start_time = time.time()
        self.data_fields = {
            'body_pos_cmd': [],
            'body_vel_cmd': [],
            'body_ang_vel_cmd': [],
            'body_pos': [],
            'body_vel': [],
            'body_ori': [],
            'body_ang_vel': []
        }
        # 添加误差计算所需的字段
        self.position_errors = []
        self.velocity_errors = []
        self.angular_velocity_errors = []
    
    def add_data(self, wbc_data):
        current_time = time.time() - self.start_time
        self.timestamps.append(current_time)
        
        for field in self.data_fields.keys():
            if hasattr(wbc_data, field):
                value = getattr(wbc_data, field)
                self.data_fields[field].append(value)
                
                # 实时计算误差
                if field == 'body_pos_cmd' and self.data_fields['body_pos']:
                    idx = len(self.data_fields['body_pos']) - 1
                    error = np.array(value) - np.array(self.data_fields['body_pos'][idx])
                    self.position_errors.append(np.linalg.norm(error))
                
                if field == 'body_vel_cmd' and self.data_fields['body_vel']:
                    idx = len(self.data_fields['body_vel']) - 1
                    error = np.array(value) - np.array(self.data_fields['body_vel'][idx])
                    self.velocity_errors.append(np.linalg.norm(error))
                
                if field == 'body_ang_vel_cmd' and self.data_fields['body_ang_vel']:
                    idx = len(self.data_fields['body_ang_vel']) - 1
                    error = np.array(value) - np.array(self.data_fields['body_ang_vel'][idx])
                    self.angular_velocity_errors.append(np.linalg.norm(error))
    
    def save_to_csv(self, filename=None):
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"wbc_data_{timestamp}.csv"
        
        with open(filename, 'w', newline='') as f:
            writer = csv.writer(f)
            headers = ['Time']
            for field in self.data_fields.keys():
                dim_count = len(self.data_fields[field][0]) if self.data_fields[field] else 0
                dim_labels = ['X', 'Y', 'Z', 'W'][:dim_count]
                headers.extend([f"{field}_{dim}" for dim in dim_labels])
            writer.writerow(headers)
            
            for i, t in enumerate(self.timestamps):
                row = [t]
                for field in self.data_fields.keys():
                    if i < len(self.data_fields[field]):
                        row.extend(self.data_fields[field][i])
                writer.writerow(row)
        
        print(f"Data saved to {filename}")
        return filename
    
    def calculate_error_metrics(self):
        """计算并返回位置、速度和角速度的平均误差"""
        metrics = {
            'position': {'mean': 0.0, 'max': 0.0, 'min': 0.0, 'std': 0.0},
            'velocity': {'mean': 0.0, 'max': 0.0, 'min': 0.0, 'std': 0.0},
            'angular_velocity': {'mean': 0.0, 'max': 0.0, 'min': 0.0, 'std': 0.0}
        }
        
        if self.position_errors:
            pos_errors = np.array(self.position_errors)
            metrics['position']['mean'] = np.mean(pos_errors)
            metrics['position']['max'] = np.max(pos_errors)
            metrics['position']['min'] = np.min(pos_errors)
            metrics['position']['std'] = np.std(pos_errors)
        
        if self.velocity_errors:
            vel_errors = np.array(self.velocity_errors)
            metrics['velocity']['mean'] = np.mean(vel_errors)
            metrics['velocity']['max'] = np.max(vel_errors)
            metrics['velocity']['min'] = np.min(vel_errors)
            metrics['velocity']['std'] = np.std(vel_errors)
        
        if self.angular_velocity_errors:
            ang_vel_errors = np.array(self.angular_velocity_errors)
            metrics['angular_velocity']['mean'] = np.mean(ang_vel_errors)
            metrics['angular_velocity']['max'] = np.max(ang_vel_errors)
            metrics['angular_velocity']['min'] = np.min(ang_vel_errors)
            metrics['angular_velocity']['std'] = np.std(ang_vel_errors)
        
        return metrics

def handle_wbc_lcm_data(channel, data):
    try:
        wbc_data = wbc_test_data_t.decode(data)
        data_storage.add_data(wbc_data)
        
        if len(data_storage.timestamps) % 100 == 0:
            print(f"Received {len(data_storage.timestamps)} messages...")
            
    except Exception as e:
        print(f"Error processing message: {e}")

def print_error_metrics(metrics):
    """格式化打印误差指标"""
    print("\n" + "="*50)
    print("Performance Metrics Summary:")
    print("="*50)
    
    for error_type, stats in metrics.items():
        if stats['mean'] > 0:  # 只打印有数据的指标
            print(f"\n{error_type.replace('_', ' ').title()} Errors:")
            print(f"  Mean: {stats['mean']:.6f}")
            print(f"  Min:  {stats['min']:.6f}")
            print(f"  Max:  {stats['max']:.6f}")
            print(f"  Std:  {stats['std']:.6f}")
    
    print("="*50 + "\n")

def main():
    global data_storage
    data_storage = DataStorage()
    
    lc = lcm.LCM()
    subscription = lc.subscribe("wbc_lcm_data", handle_wbc_lcm_data)
    
    print("Listening for messages on wbc_lcm_data channel...")
    print("Collecting data for 10 seconds...")
    
    start_time = time.time()
    collection_duration = 10  # 秒
    
    try:
        while time.time() - start_time < collection_duration:
            lc.handle_timeout(100)
    except Exception as e:
        print(f"Error during data collection: {e}")
    
    print(f"\nData collection complete. Collected {len(data_storage.timestamps)} messages in {collection_duration} seconds.")
    
    if not data_storage.timestamps:
        print("No data received. Exiting...")
        return
    
    # 计算并打印误差指标
    error_metrics = data_storage.calculate_error_metrics()
    print_error_metrics(error_metrics)
    
    # 保存数据
    csv_filename = data_storage.save_to_csv()
    
    # 创建图表
    fig, axs = plt.subplots(4, 1, figsize=(12, 16), sharex=True)
    plt.subplots_adjust(hspace=0.4)
    t = np.array(data_storage.timestamps)
    
    # 1. 位置对比
    pos_cmd = np.array(data_storage.data_fields['body_pos_cmd'])
    pos_act = np.array(data_storage.data_fields['body_pos'])
    for i in range(3):
        axs[0].plot(t, pos_cmd[:, i], '--', label=f'Cmd {["X","Y","Z"][i]}', alpha=0.7)
        axs[0].plot(t, pos_act[:, i], '-', label=f'Actual {["X","Y","Z"][i]}')
    axs[0].set_title('Body Position')
    axs[0].set_ylabel('Position (m)')
    axs[0].grid(True)
    axs[0].legend(ncol=3)
    
    # 2. 速度对比
    vel_cmd = np.array(data_storage.data_fields['body_vel_cmd'])
    vel_act = np.array(data_storage.data_fields['body_vel'])
    for i in range(3):
        axs[1].plot(t, vel_cmd[:, i], '--', label=f'Cmd {["X","Y","Z"][i]}', alpha=0.7)
        axs[1].plot(t, vel_act[:, i], '-', label=f'Actual {["X","Y","Z"][i]}')
    axs[1].set_title('Body Velocity')
    axs[1].set_ylabel('Velocity (m/s)')
    axs[1].grid(True)
    axs[1].legend(ncol=3)
    
    # 3. 角速度对比
    ang_vel_cmd = np.array(data_storage.data_fields['body_ang_vel_cmd'])
    ang_vel_act = np.array(data_storage.data_fields['body_ang_vel'])
    for i in range(3):
        axs[2].plot(t, ang_vel_cmd[:, i], '--', label=f'Cmd {["X","Y","Z"][i]}', alpha=0.7)
        axs[2].plot(t, ang_vel_act[:, i], '-', label=f'Actual {["X","Y","Z"][i]}')
    axs[2].set_title('Body Angular Velocity')
    axs[2].set_ylabel('Ang Vel (rad/s)')
    axs[2].grid(True)
    axs[2].legend(ncol=3)
    
    # 4. 朝向
    ori = np.array(data_storage.data_fields['body_ori'])
    for i, label in enumerate(['X', 'Y', 'Z', 'W']):
        if i < ori.shape[1]:
            axs[3].plot(t, ori[:, i], '-', label=f'Orientation {label}')
    axs[3].set_title('Body Orientation')
    axs[3].set_ylabel('Value')
    axs[3].set_xlabel('Time (s)')
    axs[3].grid(True)
    axs[3].legend(ncol=2)
    
    # 设置X轴范围
    min_time = max(0, t[0] - 0.1)
    max_time = min(t[-1] + 0.1, collection_duration)
    for ax in axs:
        ax.set_xlim(min_time, max_time)
    
    # 保存和显示图表
    plot_filename = csv_filename.replace('.csv', '.png')
    plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
    print(f"Plot saved to {plot_filename}")
    
    # 添加误差图表
    fig_errors, ax_errors = plt.subplots(3, 1, figsize=(12, 12))
    
    # 位置误差
    if data_storage.position_errors:
        ax_errors[0].plot(t[:len(data_storage.position_errors)], data_storage.position_errors, 'r-', label='Position Error')
        ax_errors[0].axhline(y=error_metrics['position']['mean'], color='b', linestyle='--', 
                             label=f'Mean: {error_metrics["position"]["mean"]:.4f}')
        ax_errors[0].set_title('Position Error Over Time')
        ax_errors[0].set_ylabel('Error Magnitude (m)')
        ax_errors[0].grid(True)
        ax_errors[0].legend()
    
    # 速度误差
    if data_storage.velocity_errors:
        ax_errors[1].plot(t[:len(data_storage.velocity_errors)], data_storage.velocity_errors, 'g-', label='Velocity Error')
        ax_errors[1].axhline(y=error_metrics['velocity']['mean'], color='b', linestyle='--', 
                             label=f'Mean: {error_metrics["velocity"]["mean"]:.4f}')
        ax_errors[1].set_title('Velocity Error Over Time')
        ax_errors[1].set_ylabel('Error Magnitude (m/s)')
        ax_errors[1].grid(True)
        ax_errors[1].legend()
    
    # 角速度误差
    if data_storage.angular_velocity_errors:
        ax_errors[2].plot(t[:len(data_storage.angular_velocity_errors)], 
                         data_storage.angular_velocity_errors, 'b-', label='Angular Velocity Error')
        ax_errors[2].axhline(y=error_metrics['angular_velocity']['mean'], color='b', linestyle='--', 
                             label=f'Mean: {error_metrics["angular_velocity"]["mean"]:.4f}')
        ax_errors[2].set_title('Angular Velocity Error Over Time')
        ax_errors[2].set_ylabel('Error Magnitude (rad/s)')
        ax_errors[2].set_xlabel('Time (s)')
        ax_errors[2].grid(True)
        ax_errors[2].legend()
    
    plt.tight_layout()
    error_plot_filename = csv_filename.replace('.csv', '_errors.png')
    plt.savefig(error_plot_filename, dpi=300, bbox_inches='tight')
    print(f"Error plot saved to {error_plot_filename}")
    
    # 显示所有图表
    plt.show()
    
    # 清理资源
    lc.unsubscribe(subscription)

if __name__ == "__main__":
    data_storage = None
    main()