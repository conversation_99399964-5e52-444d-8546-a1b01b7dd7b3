<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MiniCheetahDebug</class>
 <widget class="QDialog" name="MiniCheetahDebug">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1105</width>
    <height>695</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_16">
   <item>
    <widget class="QFrame" name="frame_5">
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QGridLayout" name="gridLayout_9">
      <item row="0" column="1">
       <widget class="QFrame" name="frame_2">
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QGridLayout" name="gridLayout_2">
         <item row="0" column="0">
          <widget class="QLabel" name="abad_2_pos">
           <property name="text">
            <string>TextLabel</string>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QLabel" name="abad_2_vel">
           <property name="text">
            <string>TextLabel</string>
           </property>
          </widget>
         </item>
         <item row="1" column="0">
          <widget class="QLabel" name="hip_2_pos">
           <property name="text">
            <string>TextLabel</string>
           </property>
          </widget>
         </item>
         <item row="1" column="1">
          <widget class="QLabel" name="hip_2_vel">
           <property name="text">
            <string>TextLabel</string>
           </property>
          </widget>
         </item>
         <item row="2" column="0">
          <widget class="QLabel" name="knee_2_pos">
           <property name="text">
            <string>TextLabel</string>
           </property>
          </widget>
         </item>
         <item row="2" column="1">
          <widget class="QLabel" name="knee_2_vel">
           <property name="text">
            <string>TextLabel</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item row="0" column="3">
       <widget class="QFrame" name="frame_4">
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QGridLayout" name="gridLayout_4">
         <item row="0" column="0">
          <widget class="QLabel" name="abad_4_pos">
           <property name="text">
            <string>TextLabel</string>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QLabel" name="abad_4_vel">
           <property name="text">
            <string>TextLabel</string>
           </property>
          </widget>
         </item>
         <item row="1" column="0">
          <widget class="QLabel" name="hip_4_pos">
           <property name="text">
            <string>TextLabel</string>
           </property>
          </widget>
         </item>
         <item row="1" column="1">
          <widget class="QLabel" name="hip_4_vel">
           <property name="text">
            <string>TextLabel</string>
           </property>
          </widget>
         </item>
         <item row="2" column="0">
          <widget class="QLabel" name="knee_4_pos">
           <property name="text">
            <string>TextLabel</string>
           </property>
          </widget>
         </item>
         <item row="2" column="1">
          <widget class="QLabel" name="knee_4_vel">
           <property name="text">
            <string>TextLabel</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item row="1" column="2">
       <widget class="QFrame" name="frame_8">
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_6">
         <item>
          <widget class="QCheckBox" name="abad_3_enable">
           <property name="text">
            <string>Enable</string>
           </property>
          </widget>
         </item>
         <item>
          <layout class="QGridLayout" name="gridLayout_11">
           <item row="1" column="1">
            <widget class="QLabel" name="label_35">
             <property name="text">
              <string>KD</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QLabel" name="label_36">
             <property name="text">
              <string>KP</string>
             </property>
            </widget>
           </item>
           <item row="0" column="0">
            <widget class="QLineEdit" name="abad_3_kp"/>
           </item>
           <item row="1" column="0">
            <widget class="QLineEdit" name="abad_3_kd"/>
           </item>
          </layout>
         </item>
         <item>
          <widget class="QSlider" name="abad_3_qd">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QFrame" name="frame_6">
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout">
         <item>
          <widget class="QCheckBox" name="abad_1_enable">
           <property name="text">
            <string>Enable</string>
           </property>
          </widget>
         </item>
         <item>
          <layout class="QGridLayout" name="gridLayout_5">
           <item row="1" column="1">
            <widget class="QLabel" name="label_26">
             <property name="text">
              <string>KD</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QLabel" name="label_25">
             <property name="text">
              <string>KP</string>
             </property>
            </widget>
           </item>
           <item row="0" column="0">
            <widget class="QLineEdit" name="abad_1_kp"/>
           </item>
           <item row="1" column="0">
            <widget class="QLineEdit" name="abad_1_kd"/>
           </item>
          </layout>
         </item>
         <item>
          <widget class="QSlider" name="abad_1_qd">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item row="0" column="2">
       <widget class="QFrame" name="frame_3">
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QGridLayout" name="gridLayout_3">
         <item row="0" column="0">
          <widget class="QLabel" name="abad_3_pos">
           <property name="text">
            <string>TextLabel</string>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QLabel" name="abad_3_vel">
           <property name="text">
            <string>TextLabel</string>
           </property>
          </widget>
         </item>
         <item row="1" column="0">
          <widget class="QLabel" name="hip_3_pos">
           <property name="text">
            <string>TextLabel</string>
           </property>
          </widget>
         </item>
         <item row="1" column="1">
          <widget class="QLabel" name="hip_3_vel">
           <property name="text">
            <string>TextLabel</string>
           </property>
          </widget>
         </item>
         <item row="2" column="0">
          <widget class="QLabel" name="knee_3_pos">
           <property name="text">
            <string>TextLabel</string>
           </property>
          </widget>
         </item>
         <item row="2" column="1">
          <widget class="QLabel" name="knee_3_vel">
           <property name="text">
            <string>TextLabel</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QFrame" name="frame_7">
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_5">
         <item>
          <widget class="QCheckBox" name="abad_2_enable">
           <property name="text">
            <string>Enable</string>
           </property>
          </widget>
         </item>
         <item>
          <layout class="QGridLayout" name="gridLayout_10">
           <item row="1" column="1">
            <widget class="QLabel" name="label_33">
             <property name="text">
              <string>KD</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QLabel" name="label_34">
             <property name="text">
              <string>KP</string>
             </property>
            </widget>
           </item>
           <item row="0" column="0">
            <widget class="QLineEdit" name="abad_2_kp"/>
           </item>
           <item row="1" column="0">
            <widget class="QLineEdit" name="abad_2_kd"/>
           </item>
          </layout>
         </item>
         <item>
          <widget class="QSlider" name="abad_2_qd">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item row="1" column="3">
       <widget class="QFrame" name="frame_9">
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_7">
         <item>
          <widget class="QCheckBox" name="abad_4_enable">
           <property name="text">
            <string>Enable</string>
           </property>
          </widget>
         </item>
         <item>
          <layout class="QGridLayout" name="gridLayout_12">
           <item row="1" column="1">
            <widget class="QLabel" name="label_37">
             <property name="text">
              <string>KD</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QLabel" name="label_38">
             <property name="text">
              <string>KP</string>
             </property>
            </widget>
           </item>
           <item row="0" column="0">
            <widget class="QLineEdit" name="abad_4_kp"/>
           </item>
           <item row="1" column="0">
            <widget class="QLineEdit" name="abad_4_kd"/>
           </item>
          </layout>
         </item>
         <item>
          <widget class="QSlider" name="abad_4_qd">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item row="0" column="0">
       <widget class="QFrame" name="frame">
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QGridLayout" name="gridLayout">
         <item row="0" column="0">
          <widget class="QLabel" name="abad_1_pos">
           <property name="text">
            <string>TextLabel</string>
           </property>
          </widget>
         </item>
         <item row="0" column="1">
          <widget class="QLabel" name="abad_1_vel">
           <property name="text">
            <string>TextLabel</string>
           </property>
          </widget>
         </item>
         <item row="1" column="0">
          <widget class="QLabel" name="hip_1_pos">
           <property name="text">
            <string>TextLabel</string>
           </property>
          </widget>
         </item>
         <item row="1" column="1">
          <widget class="QLabel" name="hip_1_vel">
           <property name="text">
            <string>TextLabel</string>
           </property>
          </widget>
         </item>
         <item row="2" column="0">
          <widget class="QLabel" name="knee_1_pos">
           <property name="text">
            <string>TextLabel</string>
           </property>
          </widget>
         </item>
         <item row="2" column="1">
          <widget class="QLabel" name="knee_1_vel">
           <property name="text">
            <string>TextLabel</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QFrame" name="frame_10">
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_8">
         <item>
          <widget class="QCheckBox" name="hip_1_enable">
           <property name="text">
            <string>Enable</string>
           </property>
          </widget>
         </item>
         <item>
          <layout class="QGridLayout" name="gridLayout_13">
           <item row="1" column="1">
            <widget class="QLabel" name="label_39">
             <property name="text">
              <string>KD</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QLabel" name="label_40">
             <property name="text">
              <string>KP</string>
             </property>
            </widget>
           </item>
           <item row="0" column="0">
            <widget class="QLineEdit" name="hip_1_kp"/>
           </item>
           <item row="1" column="0">
            <widget class="QLineEdit" name="hip_1_kd"/>
           </item>
          </layout>
         </item>
         <item>
          <widget class="QSlider" name="hip_1_qd">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="QFrame" name="frame_11">
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_9">
         <item>
          <widget class="QCheckBox" name="knee_1_enable">
           <property name="text">
            <string>Enable</string>
           </property>
          </widget>
         </item>
         <item>
          <layout class="QGridLayout" name="gridLayout_14">
           <item row="1" column="1">
            <widget class="QLabel" name="label_41">
             <property name="text">
              <string>KD</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QLabel" name="label_42">
             <property name="text">
              <string>KP</string>
             </property>
            </widget>
           </item>
           <item row="0" column="0">
            <widget class="QLineEdit" name="knee_1_kp"/>
           </item>
           <item row="1" column="0">
            <widget class="QLineEdit" name="knee_1_kd"/>
           </item>
          </layout>
         </item>
         <item>
          <widget class="QSlider" name="knee_1_qd">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QFrame" name="frame_12">
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_10">
         <item>
          <widget class="QCheckBox" name="hip_2_enable">
           <property name="text">
            <string>Enable</string>
           </property>
          </widget>
         </item>
         <item>
          <layout class="QGridLayout" name="gridLayout_15">
           <item row="1" column="1">
            <widget class="QLabel" name="label_43">
             <property name="text">
              <string>KD</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QLabel" name="label_44">
             <property name="text">
              <string>KP</string>
             </property>
            </widget>
           </item>
           <item row="0" column="0">
            <widget class="QLineEdit" name="hip_2_kp"/>
           </item>
           <item row="1" column="0">
            <widget class="QLineEdit" name="hip_2_kd"/>
           </item>
          </layout>
         </item>
         <item>
          <widget class="QSlider" name="hip_2_qd">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item row="3" column="1">
       <widget class="QFrame" name="frame_13">
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_11">
         <item>
          <widget class="QCheckBox" name="knee_2_enable">
           <property name="text">
            <string>Enable</string>
           </property>
          </widget>
         </item>
         <item>
          <layout class="QGridLayout" name="gridLayout_16">
           <item row="1" column="1">
            <widget class="QLabel" name="label_45">
             <property name="text">
              <string>KD</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QLabel" name="label_46">
             <property name="text">
              <string>KP</string>
             </property>
            </widget>
           </item>
           <item row="0" column="0">
            <widget class="QLineEdit" name="knee_2_kp"/>
           </item>
           <item row="1" column="0">
            <widget class="QLineEdit" name="knee_2_kd"/>
           </item>
          </layout>
         </item>
         <item>
          <widget class="QSlider" name="knee_2_qd">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item row="2" column="2">
       <widget class="QFrame" name="frame_14">
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_12">
         <item>
          <widget class="QCheckBox" name="hip_3_enable">
           <property name="text">
            <string>Enable</string>
           </property>
          </widget>
         </item>
         <item>
          <layout class="QGridLayout" name="gridLayout_17">
           <item row="1" column="1">
            <widget class="QLabel" name="label_47">
             <property name="text">
              <string>KD</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QLabel" name="label_48">
             <property name="text">
              <string>KP</string>
             </property>
            </widget>
           </item>
           <item row="0" column="0">
            <widget class="QLineEdit" name="hip_3_kp"/>
           </item>
           <item row="1" column="0">
            <widget class="QLineEdit" name="hip_3_kd"/>
           </item>
          </layout>
         </item>
         <item>
          <widget class="QSlider" name="hip_3_qd">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item row="3" column="2">
       <widget class="QFrame" name="frame_15">
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_13">
         <item>
          <widget class="QCheckBox" name="knee_3_enable">
           <property name="text">
            <string>Enable</string>
           </property>
          </widget>
         </item>
         <item>
          <layout class="QGridLayout" name="gridLayout_18">
           <item row="1" column="1">
            <widget class="QLabel" name="label_49">
             <property name="text">
              <string>KD</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QLabel" name="label_50">
             <property name="text">
              <string>KP</string>
             </property>
            </widget>
           </item>
           <item row="0" column="0">
            <widget class="QLineEdit" name="knee_3_kp"/>
           </item>
           <item row="1" column="0">
            <widget class="QLineEdit" name="knee_3_kd"/>
           </item>
          </layout>
         </item>
         <item>
          <widget class="QSlider" name="knee_3_qd">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item row="2" column="3">
       <widget class="QFrame" name="frame_16">
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_14">
         <item>
          <widget class="QCheckBox" name="hip_4_enable">
           <property name="text">
            <string>Enable</string>
           </property>
          </widget>
         </item>
         <item>
          <layout class="QGridLayout" name="gridLayout_19">
           <item row="1" column="1">
            <widget class="QLabel" name="label_51">
             <property name="text">
              <string>KD</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QLabel" name="label_52">
             <property name="text">
              <string>KP</string>
             </property>
            </widget>
           </item>
           <item row="0" column="0">
            <widget class="QLineEdit" name="hip_4_kp"/>
           </item>
           <item row="1" column="0">
            <widget class="QLineEdit" name="hip_4_kd"/>
           </item>
          </layout>
         </item>
         <item>
          <widget class="QSlider" name="hip_4_qd">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item row="3" column="3">
       <widget class="QFrame" name="frame_17">
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_15">
         <item>
          <widget class="QCheckBox" name="knee_4_enable">
           <property name="text">
            <string>Enable</string>
           </property>
          </widget>
         </item>
         <item>
          <layout class="QGridLayout" name="gridLayout_20">
           <item row="1" column="1">
            <widget class="QLabel" name="label_53">
             <property name="text">
              <string>KD</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QLabel" name="label_54">
             <property name="text">
              <string>KP</string>
             </property>
            </widget>
           </item>
           <item row="0" column="0">
            <widget class="QLineEdit" name="knee_4_kp"/>
           </item>
           <item row="1" column="0">
            <widget class="QLineEdit" name="knee_4_kd"/>
           </item>
          </layout>
         </item>
         <item>
          <widget class="QSlider" name="knee_4_qd">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Cancel|QDialogButtonBox::Ok</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>MiniCheetahDebug</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>248</x>
     <y>254</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>MiniCheetahDebug</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>316</x>
     <y>260</y>
    </hint>
    <hint type="destinationlabel">
     <x>286</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
