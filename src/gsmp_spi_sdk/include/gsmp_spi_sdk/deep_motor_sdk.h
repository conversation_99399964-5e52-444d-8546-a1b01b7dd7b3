
#include <net/if.h>
#include <sys/ioctl.h>
#include <sys/epoll.h>
#include <fcntl.h>
#include <pthread.h>
#include <stdio.h>
#include <linux/can/raw.h>
#include <sys/time.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "gsmp_spi_sdk/can_protocol.h"

enum SendRecvRet{
    //*******************************
    //SendRecvRet: SendRecv函数的返回值
    //*******************************
    //正常时返回0
    //发送长度错误返回-1  
    //接收超时错误返回-2
    //接收epoll错误返回-3
    //接收长度错误返回-4

    //*******************************
    //SendRecvRet: return value of SendRecv function
    //*******************************
    //return 0: when no error
    //return -1 send length error
    //return -2 receive timeout error
    //return -3 receive epoll error
    //return -4 receive length error
    kNoSendRecvError = 0,
    kSendLengthError = -1,
    kRecvTimeoutError = -2,
    kRecvEpollError = -3,
    kRecvLengthError = -4
};



enum MotorErrorType{
    //*******************************
    //MotorErrorType: SendRecv函数的返回值
    //*******************************
    //全为0: 无错误
    //bit 0: 过压标志位
    //bit 1: 欠压标志位
    //bit 2: 过流标志位
    //bit 3: 关节过温标志位
    //bit 4: 驱动板过温标志位
    //bit 5: Can超时标志位

    //*******************************
    //MotorErrorType: the return value of SendRecv function
    //*******************************
    //all 0: no error
    //bit 0: over voltage flag
    //bit 1: under voltage flag
    //bit 2: over current flag
    //bit 3: motor over temp flag
    //bit 4: driver board over temp flag
    //bit 5: can timeout flag
    kMotorNoError = 0,
    kOverVoltage = (0x01 << 0),
    kUnderVoltage = (0x01 << 1),
    kOverCurrent = (0x01 << 2),
    kMotorOverTemp = (0x01 << 3),
    kDriverOverTemp = (0x01 << 4),
    kCanTimeout = (0x01 << 5)
};



//存储电机返回的数据
//Struct saving data from motor
typedef struct
{
    uint8_t motor_id_;
    uint8_t cmd_;
    float position_;
    float velocity_;
    float torque_;
    bool flag_;
    float temp_;
    uint16_t error_;
}MotorDATA;


//存储发向电机的数据
//Struct of cmd sending to motor
typedef struct
{
    uint8_t motor_id_;
    uint8_t cmd_;
    float position_;
    float velocity_;
    float torque_;
    float kp_;
    float kd_;
}MotorCMD;



//DrMotorCan类，用于保存can的相关配置和资源
//DrMotorCan struct, saving can configs and resources
typedef struct{
    bool is_show_log_;
    int can_socket_;
    int epoll_fd_;
    pthread_mutex_t rw_mutex;
}DrMotorCan;
