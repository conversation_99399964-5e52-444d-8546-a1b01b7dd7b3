/*! @file LegController.cpp
 *  @brief Common Leg Control Interface
 *
 *  Implements low-level leg control for Mini Cheetah and Cheetah 3 Robots
 *  Abstracts away the difference between the SPIne and the TI Boards
 *  All quantities are in the "leg frame" which has the same orientation as the
 * body frame, but is shifted so that 0,0,0 is at the ab/ad pivot (the "hip
 * frame").
 */

#include <fstream>
#include "Controllers/LegController.h"

/*!
 * Zero the leg command so the leg will not output torque
 */
template <typename T>
void LegControllerCommand<T>::zero() {
  tauFeedForward = Vec3<T>::Zero();
  forceFeedForward = Vec3<T>::Zero();
  qDes = Vec3<T>::Zero();
  qdDes = Vec3<T>::Zero();
  pDes = Vec3<T>::Zero();
  vDes = Vec3<T>::Zero();
  kpCartesian = Mat3<T>::Zero();
  kdCartesian = Mat3<T>::Zero();
  kpJoint = Mat3<T>::Zero();
  kdJoint = Mat3<T>::Zero();
}



/*!
 * Zero the leg data
 */
template <typename T>
void LegControllerData<T>::zero() {
  q = Vec3<T>::Zero();
  qd = Vec3<T>::Zero();
  p = Vec3<T>::Zero();
  v = Vec3<T>::Zero();
  J = Mat3<T>::Zero();
  tauEstimate = Vec3<T>::Zero();
}

/*!
 * Zero all leg commands.  This should be run *before* any control code, so if
 * the control code is confused and doesn't change the leg command, the legs
 * won't remember the last command.
 */
template <typename T>
void LegController<T>::zeroCommand() {
  // for (auto& cmd : commands) {
  //   cmd.zero();
  // }
  _legsEnabled = false;
}

/*!
 * Set the leg to edamp.  This overwrites all command data and generates an
 * emergency damp command using the given gain. For the mini-cheetah, the edamp
 * gain is Nm/(rad/s), and for the Cheetah 3 it is N/m. You still must call
 * updateCommand for this command to end up in the low-level command data!
 */
template <typename T>
void LegController<T>::edampCommand(RobotType robot, T gain) {
  zeroCommand();
  if (robot == RobotType::CHEETAH_3) {
    for (int leg = 0; leg < 4; leg++) {
      for (int axis = 0; axis < 3; axis++) {
        commands[leg].kdCartesian(axis, axis) = gain;
      }
    }
  } else {  // mini-cheetah
    for (int leg = 0; leg < 4; leg++) {
      for (int axis = 0; axis < 3; axis++) {
        commands[leg].kdJoint(axis, axis) = gain;
      }
    }
  }
}

template <typename T>
void LegController<T>::signalHandler(int signum) {
    std::cout << "接收到 SIGINT 信号，程序即将终止。" << std::endl;
    // 终止程序
    // emergency_flag = true;
    // for (int i = 0; i < 12; ++i)
    // {
    //   sendCommand12_[i].kp=0;
    //   sendCommand12_[i].kd=2;
    //   sendCommand12_[i].tau_ff=0;
    // } 
    // sendCommand12ToSlave();

    // for(int re=0;re<3;re++)
    //   {
    //     spiCan_->disableAllDevices();
    //     spiCan_->Master_Send_Cmd();
    //     spiCan_->Slave_Data_Get(false);
    //     std::this_thread::sleep_for(std::chrono::duration<double>(0.002));
    //   }
    exit(signum);
}

template <typename T>
void LegController<T>::emergencyStop() {
    std::cout << "接收到 emergency 信号，程序即将终止。" << std::endl;
    // 终止程序
    emergency_flag = true;
    transform_count++;
    // std::cout<<"transform_count:"<<transform_count<<std::endl;
    if(transform_count>20 && transform_count<30)
    {
      // fsm_states_msg_.data = 8;
      // fsm_states_pub_.publish(fsm_states_msg_);

      for(int re=0;re<3;re++)
      {
        spiCan_->disableAllDevices();
        spiCan_->Master_Send_Cmd();
        spiCan_->Slave_Data_Get(false);
        std::this_thread::sleep_for(std::chrono::duration<double>(0.002));
      }

      // ros::shutdown(); 
      exit(0);
    }

    for (int i = 0; i < 12; ++i)
    {
      sendCommand12_[i].kp=0;
      sendCommand12_[i].kd=3;
      sendCommand12_[i].tau_ff=0;
    } 
    sendCommand12ToSlave();
    
}

template <typename T>
void LegController<T>::initpub(void)
{
  this->q_rel_pub = nt.advertise<sensor_msgs::JointState>("/ysc3go/q_rel", 1000);
  this->q_cmd_pub = nt.advertise<sensor_msgs::JointState>("/ysc3go/q_cmd", 1000);

  if (!this->q_rel_pub || !this->q_cmd_pub) {
        ROS_ERROR("Failed to initialize publishers!");
  }
}

template <typename T>
void LegController<T>::init(void) {

  spiCan_ = std::make_shared<gsmp::spiCan>();
  int fd = spiCan_->spiInit();
  if (fd < 0) {
      printf("SPI initial failed\n");
      // return -1;
  }else{
      printf("SPI initialed\n");
  }

  signal(SIGINT, signalHandler);

  // auto workStatus = master->getWorkingState();
  // if((int)workStatus)
  // {
  //   std::cout<<"######################Ethercat INIT FAILED!!"<<std::endl;
  //   emergency_flag = true;
  // }

  // enable ysc3go motor
  for(int re=0;re<5;re++)
  {
    // transTimer_.startTimer();
    spiCan_->enableAllDevices();
    spiCan_->Master_Send_Cmd();
    spiCan_->Slave_Data_Get(false);
    std::this_thread::sleep_for(std::chrono::duration<double>(0.002));
    std::cout<<" enable :"<<re<<std::endl;
    // transTimer_.endTimer();
    // std::cout<<"trans time:"<<transTimer_.getLastIntervalInMilliseconds()<<std::endl;
  }
  // std::array<gsmp::MotorCommand, 12> command_init{0};
  // 显式初始化 MotorCommand 结构体的所有成员
  std::array<gsmp::MotorCommand, 12> command_init;
  for (auto& cmd : command_init) {
      cmd.des_pos = 0;
      cmd.des_vel = 0;
      cmd.kp = 0;
      cmd.kd = 0;
      cmd.tau_ff = 0;
  }

  for(int k=0;k<10;k++){
    
    spiCan_->Master_Command_Set(command_init);
    spiCan_->Master_Send_Cmd();
    spiCan_->Slave_Data_Get(false);
    std::this_thread::sleep_for(std::chrono::duration<double>(0.03));
    // get to motorStates_
    // getMotorStatesToMotorStates();
    auto slave1_states = spiCan_->getMotorStates();
    
    for (int i = 0; i < 12; ++i) {
      motorStates_[i] = slave1_states[i];
    }
    
    // for pos trans
    for(int i=0;i<12;i++)
    {
      transform_CurrentPos[i] = (motorStates_[i].pos - base_motor[i])*direction_motor[i];
      std::cout << "INIT : " << i << " motor pos: " << motorStates_[i].pos <<  std::endl;
    }
  }
  // add for safetyCheck();
  // std::string initCheck;
  // root_nh.getParam("initCheck", initCheck);
  // if(initCheck == "yes") {
    initSafetyCheck();
  // }

      // for read
  spiCan_->Master_Command_Set(command_init);
  spiCan_->Master_Send_Cmd();

}

template <typename T>
void LegController<T>::initSafetyCheck(void)
{
  for(int i=0;i<12;i++)
  {
    auto del = fabs(transform_CurrentPos[i] - transform_getDown_TargetPos[i]);
      if(del > 0.52)
      {
        emergency_flag = true;
        ROS_INFO_STREAM("emergency state: Safety Check fail !!!");
        std::cout<< "ERROR  init Safety Check:"<<i<<" "<<transform_CurrentPos[i]<<" "<<transform_getDown_TargetPos[i]<<std::endl;
      }
  }
}

template <typename T>
void LegController<T>::sendCommand12ToSlave()
{
  static int statusCount_=0;
  ++statusCount_;

  for (int i = 0; i < 12; ++i) {
    sendCommand_salve1[i] = sendCommand12_[i];
  }
  if(statusCount_ %100==0){
    spiCan_->setStatusWord();
  }else{
    spiCan_->Master_Command_Set(sendCommand_salve1);
  }
  spiCan_->Master_Send_Cmd();
  
}

/*!
 * Update the "leg data" from a SPIne board message
 */
#ifdef SIMULATION
template <typename T>
void LegController<T>::updateData(const SpiData* spiData) {
  for (int leg = 0; leg < 4; leg++) {
    // q:
     datas[leg].q(0) = spiData->q_abad[leg];
    datas[leg].q(1) = spiData->q_hip[leg];
    datas[leg].q(2) = spiData->q_knee[leg];

    // qd
    datas[leg].qd(0) = spiData->qd_abad[leg];
    datas[leg].qd(1) = spiData->qd_hip[leg];
    datas[leg].qd(2) = spiData->qd_knee[leg];

    datas[leg].tauActuatual(0)= spiData->tau_abad[leg];
    datas[leg].tauActuatual(1) = spiData->tau_hip[leg];
    datas[leg].tauActuatual(2) = spiData->tau_knee[leg];
//    if(leg == 3)
//        printf("leg tau: %.2f\t%.2f\t%.2f\n",datas[leg].tauActuatual(0),datas[leg].tauActuatual(1),datas[leg].tauActuatual(2));
//    // J and p
    computeLegJacobianAndPosition<T>(_quadruped, datas[leg].q, &(datas[leg].J),
                                     &(datas[leg].p), leg);

    // v
    datas[leg].v = datas[leg].J * datas[leg].qd;

    Mat3<T> inv_JT = datas[leg].J.transpose().inverse();
    datas[leg].footforceDesired = inv_JT*commands[leg].tauFeedForward;
    datas[leg].footforceActuatual = inv_JT*datas[leg].tauActuatual;


//    if(leg == 3)
//        printf("fxyz : %.2f\t%.2f\t%.2f\n",datas[leg].footforceActuatual(0),datas[leg].footforceActuatual(1),datas[leg].footforceActuatual(2));

  }
   //std::cout<<"datas[leg].qd:"<<datas[0].qd<<std::endl;
// static int v0times=0;
//  v0times++;
//  if(v0times%100==0)
//    std::cout<<"leg v0:"<<datas[0].v<<std::endl;

  Vec12<T> q_rel;
  for (int leg = 0; leg < 4; leg++) {
    for (int joint = 0; joint < 3; joint++) {
        q_rel[leg*3 + joint] = datas[leg].q(joint);
    }
  }
  // publishJointStates(q_rel);
}
#endif

#ifdef REAL_ROBOT
template <typename T>
void LegController<T>::updateData() {
    spiCan_->Slave_Data_Get(false);
    auto slave1_states = spiCan_->getMotorStates();

    for (int i = 0; i < 12; ++i) {
      motorStates_[i] = slave1_states[i];
    }

    for (int i = 0 ; i < 4 ; i++){
      datas[i].q(0) = (motorStates_[i*3+0].pos - base_motor[i*3+0]) * direction_motor[i*3+0];
      datas[i].q(1) = (motorStates_[i*3+1].pos - base_motor[i*3+1]) * direction_motor[i*3+1];
      datas[i].q(2) = (motorStates_[i*3+2].pos - base_motor[i*3+2]) * direction_motor[i*3+2];

      datas[i].qd(0) = motorStates_[i*3+0].vel * direction_motor[i*3+0];
      datas[i].qd(1) = motorStates_[i*3+1].vel * direction_motor[i*3+1];
      datas[i].qd(2) = motorStates_[i*3+2].vel * direction_motor[i*3+2];

      datas[i].tauActuatual(0) = motorStates_[i*3+0].tau * direction_motor[i*3+0];
      datas[i].tauActuatual(1) = motorStates_[i*3+1].tau * direction_motor[i*3+1];
      datas[i].tauActuatual(2) = motorStates_[i*3+2].tau * direction_motor[i*3+2];

      computeLegJacobianAndPosition<T>(_quadruped, datas[i].q, &(datas[i].J),
                                        &(datas[i].p), i);

    // v
     datas[i].v = datas[i].J * datas[i].qd;

    Mat3<T> inv_JT = datas[i].J.transpose().inverse();
     datas[i].footforceDesired = inv_JT*commands[i].tauFeedForward;
     datas[i].footforceActuatual = inv_JT*datas[i].tauActuatual;
    }

    //emergency
    auto can_loss = spiCan_->getMotorCommunicationState();
    // if(fristPint){
      for(int index=0; index<12; index++){
        if(!can_loss[index]){
          emergency_flag = true;
          // fristPint =false;
          ROS_INFO_STREAM("emergency state: CAN  LOSS !!! index:"<<index);
        }
      }

  // Vec12<T> q_rel;
  // for (int leg = 0; leg < 4; leg++) {
  //   for (int joint = 0; joint < 3; joint++) {
  //       q_rel[leg*3 + joint] = motorStates_[leg*3 + joint].pos;
  //   }
  // }

  JointData jointData;
  this->convertJointDataToData(motorStates_, jointData);
  publishJointStates(jointData);

}
#endif 


/*!
 * Update the "leg data" from a TI Board message
 */
template <typename T>
void LegController<T>::updateData(const TiBoardData* tiBoardData) {
  for (int leg = 0; leg < 4; leg++) {
    for (int joint = 0; joint < 3; joint++) {
      datas[leg].q(joint) = tiBoardData[leg].q[joint];
      datas[leg].qd(joint) = tiBoardData[leg].dq[joint];
      datas[leg].p(joint) = tiBoardData[leg].position[joint];
      datas[leg].v(joint) = tiBoardData[leg].velocity[joint];

      // J and p
      computeLegJacobianAndPosition<T>(_quadruped, datas[leg].q, &datas[leg].J,
                                       nullptr, leg);
      datas[leg].tauEstimate[joint] = tiBoardData[leg].tau[joint];
    }
    //printf("%d leg, position: %f, %f, %f\n", leg, datas[leg].p[0], datas[leg].p[1], datas[leg].p[2]);
    //printf("%d leg, velocity: %f, %f, %f\n", leg, datas[leg].v[0], datas[leg].v[1], datas[leg].v[2]);
  }
}

/*!
 * Update the "leg command" for the SPIne board message
 */
#ifdef SIMULATION
template <typename T>
void LegController<T>::updateCommand(SpiCommand* spiCommand) {
  for (int leg = 0; leg < 4; leg++) {
    // tauFF
    Vec3<T> legTorque = commands[leg].tauFeedForward;

    // forceFF
    Vec3<T> footForce = commands[leg].forceFeedForward;

    // cartesian PD
    footForce +=
        commands[leg].kpCartesian * (commands[leg].pDes - datas[leg].p);
    footForce +=
        commands[leg].kdCartesian * (commands[leg].vDes - datas[leg].v);

    // Torque
    legTorque += datas[leg].J.transpose() * footForce;

    //set command:
    spiCommand->tau_abad_ff[leg] = legTorque(0);
    spiCommand->tau_hip_ff[leg] = legTorque(1);
    spiCommand->tau_knee_ff[leg] = legTorque(2);

    // joint space pd
    // joint space PD
    spiCommand->kd_abad[leg] = commands[leg].kdJoint(0, 0);
    spiCommand->kd_hip[leg] = commands[leg].kdJoint(1, 1);
    spiCommand->kd_knee[leg] = commands[leg].kdJoint(2, 2);

    spiCommand->kp_abad[leg] = commands[leg].kpJoint(0, 0);
    spiCommand->kp_hip[leg] = commands[leg].kpJoint(1, 1);
    spiCommand->kp_knee[leg] = commands[leg].kpJoint(2, 2);

    spiCommand->q_des_abad[leg] = commands[leg].qDes(0);
    spiCommand->q_des_hip[leg] = commands[leg].qDes(1);
    spiCommand->q_des_knee[leg] = commands[leg].qDes(2);

    spiCommand->qd_des_abad[leg] = commands[leg].qdDes(0);
    spiCommand->qd_des_hip[leg] = commands[leg].qdDes(1);
    spiCommand->qd_des_knee[leg] = commands[leg].qdDes(2);

    // estimate torque
    datas[leg].tauEstimate =
        legTorque +
        commands[leg].kpJoint * (commands[leg].qDes - datas[leg].q) +
        commands[leg].kdJoint * (commands[leg].qdDes - datas[leg].qd);

    spiCommand->flags[leg] = _legsEnabled ? 1 : 0;
  }

  JointCommandData cmdData;
  this->convertSpiToJointData(spiCommand, cmdData);
  publishJointCommands(cmdData);
}
#endif

#ifdef REAL_ROBOT
template <typename T>
void LegController<T>::updateCommand() {
  
  if(emergency_flag)
  // if(emergency_flag ||emergency_ctrl_c)
  {

    transform_count++;
    // std::cout<<"transform_count:"<<transform_count<<std::endl;
    if(transform_count>20 && transform_count < 30)
    {
      // fsm_states_msg_.data = 8;
      // fsm_states_pub_.publish(fsm_states_msg_);

      for(int re=0;re<3;re++)
      {
        spiCan_->disableAllDevices();
        spiCan_->Master_Send_Cmd();
        spiCan_->Slave_Data_Get(false);
        std::this_thread::sleep_for(std::chrono::duration<double>(0.002));
      }
      // if(emergency_ctrl_c){
      //   printf("!!!!!!!!!!!!!!!! CTRL C!!!!!!!!!!!!!!");
        
      // }
    }

    for (int i = 0; i < 12; ++i)
    {
      sendCommand12_[i].kp=0;
      sendCommand12_[i].kd=3;
      sendCommand12_[i].tau_ff=0;
    } 
    sendCommand12ToSlave();
  }else{
    for (int leg = 0; leg < 4; leg++) {
    // tauFF
    Vec3<T> legTorque = commands[leg].tauFeedForward;

    // forceFF
    Vec3<T> footForce = commands[leg].forceFeedForward;

    // cartesian PD
    footForce +=
        commands[leg].kpCartesian * (commands[leg].pDes - datas[leg].p);
    footForce +=
        commands[leg].kdCartesian * (commands[leg].vDes - datas[leg].v);

    // Torque
    legTorque += datas[leg].J.transpose() * footForce;

    sendCommand12_[leg*3+0].des_pos = commands[leg].qDes(0) * direction_motor[leg*3+0] + base_motor[leg*3+0];
    sendCommand12_[leg*3+1].des_pos = commands[leg].qDes(1) * direction_motor[leg*3+1] + base_motor[leg*3+1];
    sendCommand12_[leg*3+2].des_pos = commands[leg].qDes(2) * direction_motor[leg*3+2] + base_motor[leg*3+2];
    
    sendCommand12_[leg*3+0].des_vel = commands[leg].qdDes(0) * direction_motor[leg*3+0];
    sendCommand12_[leg*3+1].des_vel = commands[leg].qdDes(1) * direction_motor[leg*3+1];
    sendCommand12_[leg*3+2].des_vel = commands[leg].qdDes(2) * direction_motor[leg*3+2];

    sendCommand12_[leg*3+0].kp = commands[leg].kpJoint(0, 0);
    sendCommand12_[leg*3+1].kp = commands[leg].kpJoint(1, 1);
    sendCommand12_[leg*3+2].kp = commands[leg].kpJoint(2, 2);

    sendCommand12_[leg*3+0].kd = commands[leg].kdJoint(0, 0);
    sendCommand12_[leg*3+1].kd = commands[leg].kdJoint(1, 1);
    sendCommand12_[leg*3+2].kd = commands[leg].kdJoint(2, 2);

    sendCommand12_[leg*3+0].tau_ff = legTorque(0) * direction_motor[leg*3+0];
    sendCommand12_[leg*3+1].tau_ff = legTorque(1) * direction_motor[leg*3+1];
    sendCommand12_[leg*3+2].tau_ff = legTorque(2) * direction_motor[leg*3+2];

    // estimate torque
    datas[leg].tauEstimate =
        legTorque +
        commands[leg].kpJoint * (commands[leg].qDes - datas[leg].q) +
        commands[leg].kdJoint * (commands[leg].qdDes - datas[leg].qd);
    //添加电机最大输出扭矩约束
    for (int mx=0;mx<3;mx++)
        if(fabs(datas[leg].tauEstimate[mx]))
            datas[leg].tauEstimate[mx]=datas[leg].tauEstimate[mx]/fabs(datas[leg].tauEstimate[mx])*18.0;

      // spiCommand->flags[leg] = _legsEnabled ? 1 : 0;
  }
    sendCommand12ToSlave();
  }

  JointCommandData cmdData;
  this->convertSend12ToJointData(sendCommand12_.data(), cmdData);
  publishJointCommands(cmdData);
}
#endif

constexpr float CHEETAH_3_ZERO_OFFSET[4][3] = {{1.f, 4.f, 7.f},
                                               {2.f, 5.f, 8.f},
                                               {3.f, 6.f, 9.f}};
/*!
 * Update the "leg command" for the TI Board
 */
template <typename T>
void LegController<T>::updateCommand(TiBoardCommand* tiBoardCommand) {
  for (int leg = 0; leg < 4; leg++) {
    Vec3<T> tauFF = commands[leg].tauFeedForward.template cast<T>();


    for (int joint = 0; joint < 3; joint++) {
      tiBoardCommand[leg].kp[joint] = commands[leg].kpCartesian(joint, joint);
      tiBoardCommand[leg].kd[joint] = commands[leg].kdCartesian(joint, joint);
      tiBoardCommand[leg].tau_ff[joint] = tauFF[joint];
      tiBoardCommand[leg].position_des[joint] = commands[leg].pDes[joint];
      tiBoardCommand[leg].velocity_des[joint] = commands[leg].vDes[joint];
      tiBoardCommand[leg].force_ff[joint] =
          commands[leg].forceFeedForward[joint];
      tiBoardCommand[leg].q_des[joint] = commands[leg].qDes[joint];
      tiBoardCommand[leg].qd_des[joint] = commands[leg].qdDes[joint];
      tiBoardCommand[leg].kp_joint[joint] = commands[leg].kpJoint(joint, joint);
      tiBoardCommand[leg].kd_joint[joint] = commands[leg].kdJoint(joint, joint);
      tiBoardCommand[leg].zero_offset[joint] = CHEETAH_3_ZERO_OFFSET[leg][joint];
    }

    // please only send 1 or 0 here or the robot will explode.
    tiBoardCommand[leg].enable = _legsEnabled ? 1 : 0;
    tiBoardCommand[leg].max_torque = _maxTorque;
    tiBoardCommand[leg].zero = _zeroEncoders ? 1 : 0;
    if(_calibrateEncoders) {
      tiBoardCommand[leg].enable = _calibrateEncoders + 1;
    }

    if(_zeroEncoders) {
      tiBoardCommand[leg].enable = 0;
    }

  }
}

#ifdef SIMULATION
template <typename T>
void LegController<T>::convertSpiToJointData(const SpiCommand* spiCmd, JointCommandData& out) {
  for (int leg = 0; leg < 4; leg++) {
    // Abad, Hip, Knee 关节
    out.q_des[leg*3 + 0] = spiCmd->q_des_abad[leg];
    out.q_des[leg*3 + 1] = spiCmd->q_des_hip[leg];
    out.q_des[leg*3 + 2] = spiCmd->q_des_knee[leg];

    out.qd_des[leg*3 + 0] = spiCmd->qd_des_abad[leg];
    out.qd_des[leg*3 + 1] = spiCmd->qd_des_hip[leg];
    out.qd_des[leg*3 + 2] = spiCmd->qd_des_knee[leg];

    out.tau_ff[leg*3 + 0] = spiCmd->tau_abad_ff[leg];
    out.tau_ff[leg*3 + 1] = spiCmd->tau_hip_ff[leg];
    out.tau_ff[leg*3 + 2] = spiCmd->tau_knee_ff[leg];

  }
}
#endif

#ifdef REAL_ROBOT
template <typename T>
void LegController<T>::convertJointDataToData(const gsmp::MotorState* motorstate, JointData& in){
  for (int i = 0; i < 12; i++) {
    in.q[i] = motorstate[i].pos;
    in.qd[i] = motorstate[i].vel;
    in.tau[i] = motorstate[i].tau;
  }
}

template <typename T>
void LegController<T>::convertSend12ToJointData(const gsmp::MotorCommand* sendCmd, JointCommandData& out){
  for (int i = 0; i < 12; i++) {
    out.q_des[i] = sendCmd[i].des_pos;
    out.qd_des[i] = sendCmd[i].des_vel;
    out.tau_ff[i] = sendCmd[i].tau_ff;
  }
}
#endif


template<typename T>
void LegController<T>::publishJointStates(const JointData& jointData) {
    sensor_msgs::JointState msg;
    msg.header.stamp = ros::Time::now();
    msg.header.frame_id = "joint";
    msg.name = {
        "FL_abad", "FL_hip", "FL_knee",
        "FR_abad", "FR_hip", "FR_knee",
        "RL_abad", "RL_hip", "RL_knee",
        "RR_abad", "RR_hip", "RR_knee"
    };

    msg.position.resize(12);
    msg.velocity.resize(12);
    msg.effort.resize(12);

    for (int leg = 0; leg < 4; leg++) {
      for (int joint = 0; joint < 3; joint++) {
        int idx = leg * 3 + joint;
          msg.position[idx] = static_cast<double>(jointData.q[idx]);
          msg.velocity[idx] = static_cast<double>(jointData.qd[idx]);
          msg.effort[idx] = static_cast<double>(jointData.tau[idx]);
      }
    }
    q_rel_pub.publish(msg);
}

template <typename T>
void LegController<T>::publishJointCommands(const JointCommandData& cmdData) {
  sensor_msgs::JointState msg;
  msg.header.stamp = ros::Time::now();
  msg.header.frame_id = "joint"; 
  msg.name = {
        "FL_abad", "FL_hip", "FL_knee",
        "FR_abad", "FR_hip", "FR_knee",
        "RL_abad", "RL_hip", "RL_knee",
        "RR_abad", "RR_hip", "RR_knee"
  };
  msg.position.resize(12);
  msg.velocity.resize(12);
  msg.effort.resize(12);

  for (int leg = 0; leg < 4; leg++) {
    for (int joint = 0; joint < 3; joint++) {
      int idx = leg * 3 + joint;
        msg.position[idx] = static_cast<double>(cmdData.q_des[idx]);
        msg.velocity[idx] = static_cast<double>(cmdData.qd_des[idx]);
        msg.effort[idx] = static_cast<double>(cmdData.tau_ff[idx]);
    }
  }

  q_cmd_pub.publish(msg);
}



/*!
 * Set LCM debug data from leg commands and data
 */
template<typename T>
void LegController<T>::setLcm(leg_control_data_lcmt *lcmData, leg_control_command_lcmt *lcmCommand) {
    for(int leg = 0; leg < 4; leg++) {
        for(int axis = 0; axis < 3; axis++) {
            int idx = leg*3 + axis;
            lcmData->q[idx] = datas[leg].q[axis];
            lcmData->qd[idx] = datas[leg].qd[axis];
            lcmData->p[idx] = datas[leg].p[axis];
            lcmData->v[idx] = datas[leg].v[axis];
//            lcmData->tau_est[idx] = datas[leg].tauEstimate[axis];
            lcmData->tau_est[idx] = datas[leg].tauActuatual[axis];

//            if(leg == 3 && axis==2)
//printf("datas[leg].tauActuatual[axis] %.2f\n",lcmData->tau_est[idx]);

            lcmCommand->tau_ff[idx] = commands[leg].tauFeedForward[axis];
            lcmCommand->f_ff[idx] = commands[leg].forceFeedForward[axis];
            lcmCommand->q_des[idx] = commands[leg].qDes[axis];
            lcmCommand->qd_des[idx] = commands[leg].qdDes[axis];
            lcmCommand->p_des[idx] = commands[leg].pDes[axis];
            lcmCommand->v_des[idx] = commands[leg].vDes[axis];
            lcmCommand->kp_cartesian[idx] = commands[leg].kpCartesian(axis, axis);
            lcmCommand->kd_cartesian[idx] = commands[leg].kdCartesian(axis, axis);
            lcmCommand->kp_joint[idx] = commands[leg].kpJoint(axis, axis);
            lcmCommand->kd_joint[idx] = commands[leg].kdJoint(axis, axis);
        }
    }
}

template struct LegControllerCommand<double>;
template struct LegControllerCommand<float>;

template struct LegControllerData<double>;
template struct LegControllerData<float>;

template class LegController<double>;
template class LegController<float>;

/*!
 * Compute the position of the foot and its Jacobian.  This is done in the local
 * leg coordinate system. If J/p are NULL, the calculation will be skipped.
 */
template <typename T>
void computeLegJacobianAndPosition(Quadruped<T>& quad, Vec3<T>& q, Mat3<T>* J,
                                   Vec3<T>* p, int leg) {
  T l1 = quad._abadLinkLength;
  T l2 = quad._hipLinkLength;
  T l3 = quad._kneeLinkLength;
  T l4 = quad._kneeLinkY_offset;
  T sideSign = quad.getSideSign(leg);

  T s1 = std::sin(q(0));
  T s2 = std::sin(q(1));
  T s3 = std::sin(q(2));

  T c1 = std::cos(q(0));
  T c2 = std::cos(q(1));
  T c3 = std::cos(q(2));

  T c23 = c2 * c3 - s2 * s3;
  T s23 = s2 * c3 + c2 * s3;

  if (J) {
    J->operator()(0, 0) = 0;
    J->operator()(0, 1) = l3 * c23 + l2 * c2;
    J->operator()(0, 2) = l3 * c23;
    J->operator()(1, 0) = l3 * c1 * c23 + l2 * c1 * c2 - (l1+l4) * sideSign * s1;
    J->operator()(1, 1) = -l3 * s1 * s23 - l2 * s1 * s2;
    J->operator()(1, 2) = -l3 * s1 * s23;
    J->operator()(2, 0) = l3 * s1 * c23 + l2 * c2 * s1 + (l1+l4) * sideSign * c1;
    J->operator()(2, 1) = l3 * c1 * s23 + l2 * c1 * s2;
    J->operator()(2, 2) = l3 * c1 * s23;
  }

  if (p) {
    p->operator()(0) = l3 * s23 + l2 * s2;
    p->operator()(1) = (l1+l4) * sideSign * c1 + l3 * (s1 * c23) + l2 * c2 * s1;
    p->operator()(2) = (l1+l4) * sideSign * s1 - l3 * (c1 * c23) - l2 * c1 * c2;
  }
}

template void computeLegJacobianAndPosition<double>(Quadruped<double>& quad,
                                                    Vec3<double>& q,
                                                    Mat3<double>* J,
                                                    Vec3<double>* p, int leg);
template void computeLegJacobianAndPosition<float>(Quadruped<float>& quad,
                                                   Vec3<float>& q,
                                                   Mat3<float>* J,
                                                   Vec3<float>* p, int leg);
