/**
 * @file rt_rc_interface.h
 *
 */
#ifndef _RT_RC_INTERFACE
#define _RT_RC_INTERFACE

#include <ros/ros.h>
#include <sensor_msgs/Joy.h>

class rc_control_settings {
  public:
    double     mode;
    double     p_des[2]; // (x, y) -1 ~ 1
    double     height_variation; // -1 ~ 1
    double     v_des[3]; // -1 ~ 1 * (scale 0.5 ~ 1.5)
    double     rpy_des[3]; // -1 ~ 1
    double     omega_des[3]; // -1 ~ 1
    double     variable[4];
    double     step_height;
};

struct F710_data {
  // 摇杆
  float left_stick_x;
  float left_stick_y;
  float right_stick_x;
  float right_stick_y;
  
  // 扳机键
  float left_trigger;  // 新增
  float right_trigger; // 新增
  
  // 按钮
  bool button_A;
  bool button_B;
  bool button_X;
  bool button_Y;
  bool button_LB;
  bool button_RB;
  bool button_back;
  bool button_start;
  bool button_guide;  // 新增
  bool button_L3;     // 新增
  bool button_R3;     // 新增
  
  // D-Pad
  bool dpad_up;
  bool dpad_down;
  bool dpad_left;
  bool dpad_right;
};

namespace RC_mode{
  constexpr int OFF = 0;
  constexpr int LIE_DOWN =1;
  constexpr int READY=2;
  constexpr int QP_STAND = 3;
  constexpr int BACKFLIP_PRE = 4;
  constexpr int BACKFLIP = 5;
  constexpr int VISION = 6;

  constexpr int LOCOMOTION = 11;
  constexpr int RECOVERY_STAND = 12;
  constexpr int FRONT_JUMP=13;

  // Experiment Mode
  constexpr int TWO_LEG_STANCE_PRE = 20;
  constexpr int TWO_LEG_STANCE = 21;

  constexpr int WAIT=31;
};

void sbus_packet_complete();
void sbus_packet_complete_at9s();

int init_f710_ros();
void update_f710(F710_data* data) ;
void joy_callback(sensor_msgs::Joy msg);


void get_rc_control_settings(void* settings);
//void get_rc_channels(void* settings);

void* v_memcpy(void* dest, volatile void* src, size_t n);

float deadband(float command, float deadbandRegion, float minVal, float maxVal);

#endif
