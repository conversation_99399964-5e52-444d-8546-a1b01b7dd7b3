# @author: He<PERSON>
# @date: 2023-8-9
# @brief: bind sdk to python

cmake_minimum_required(VERSION 3.12)

project(gsmp_spi_sdk
        DESCRIPTION "SDK of Geely Smart Mobile Platform"
        VERSION 0.0.3)

find_package(catkin REQUIRED)
# add_subdirectory(third-party)

file(GLOB GSMP_SPI_SDK_SRCS src/*.cpp)

# Define directories
set(SDK_DEVEL_PREFIX ${CATKIN_DEVEL_PREFIX} CACHE STRING "SDK install path")
set(SDK_INCLUDE_DIR ${SDK_DEVEL_PREFIX}/include/gsmp_spi_sdk)
set(SDK_LIB_DIR ${SDK_DEVEL_PREFIX}/lib)
set(SDK_DOWNLOAD_DIR ${CMAKE_CURRENT_BINARY_DIR}/download)
set(SDK_BUILD_DIR ${CMAKE_CURRENT_BINARY_DIR}/build)

# Create directories if they do not exist
file(MAKE_DIRECTORY ${SDK_INCLUDE_DIR})
file(MAKE_DIRECTORY ${SDK_LIB_DIR})
file(MAKE_DIRECTORY ${SDK_DOWNLOAD_DIR})
file(MAKE_DIRECTORY ${SDK_BUILD_DIR})


file(GLOB_RECURSE HEADERS "include/gsmp_spi_sdk/*")
foreach (HEADER_FILE ${HEADERS})
    message(STATUS "FOUND HEADER: " ${HEADER_FILE})
#     file(COPY ${HEADER_FILE} DESTINATION ${SDK_INCLUDE_DIR})
    file(COPY ${HEADER_FILE} DESTINATION ${SDK_INCLUDE_DIR}/gsmp_spi_sdk)
endforeach ()


add_library(gsmp_spi_sdk SHARED ${GSMP_SPI_SDK_SRCS})
# add_dependencies(gsmp_spi_sdk soem serial)
# target_link_libraries(gsmp_spi_sdk m rt pthread soem serial ${catkin_LIBRARIES})
target_link_libraries(gsmp_spi_sdk ${catkin_LIBRARIES})
set_target_properties(gsmp_spi_sdk PROPERTIES VERSION ${PROJECT_VERSION})
target_include_directories(gsmp_spi_sdk PUBLIC include ${SOEM_INCLUDE_DIRS} ${catkin_INCLUDE_DIRS} ${SERIAL_INCLUDE_DIRS})
target_compile_options(gsmp_spi_sdk PUBLIC -fPIC -std=c++14 -Wno-error=deprecated-declarations -Wno-deprecated-declarations)
set_target_properties(gsmp_spi_sdk PROPERTIES LIBRARY_OUTPUT_DIRECTORY ${SDK_LIB_DIR})

catkin_package(
        INCLUDE_DIRS ${SDK_INCLUDE_DIR}
        LIBRARIES gsmp_spi_sdk
)
