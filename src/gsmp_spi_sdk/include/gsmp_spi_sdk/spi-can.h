
#pragma once

#include <inttypes.h>
#include <string.h>
#include <array>
#include <stdio.h>
#include <stdint.h>
#include <stdlib.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/ioctl.h>
#include <linux/types.h>
#include <linux/spi/spidev.h>
#include "gsmp_spi_sdk/deep_motor_sdk.h"
#include "gsmp_spi_sdk/MotorUtils.h"
//********************************************//
//***********Message Type*****************//
//********************************************//

#pragma pack(push, 1)
namespace gsmp {

struct canMsg
{
    uint8_t dlc;
    uint8_t sts:4;  
    uint8_t rtr:2;  
    uint8_t ide:2;  
    uint32_t id;
    uint8_t data[8];
};

typedef struct
{
    struct canMsg motor[12];
    struct canMsg bms;
    uint16_t ambient_light_cmd;
} Master_TxMsg;

typedef struct
{
    struct canMsg motor[12];  
    struct canMsg bms;    
    uint16_t reserved;
} Slave_RxMsg;


typedef struct {
    double pos, vel, tau, temp;                               // state, 最后一位是温度
} MotorState;

typedef struct {
    double des_pos, des_vel, kp, kd, tau_ff;                  // command
} MotorCommand;


class spiCan{
    public:
    // int spiInit(const char *spi_device, int speed, int bits, int mode);
    int spiInit();
    int spiTransfer(Master_TxMsg *tx_data,Slave_RxMsg *rx_data);
    void Master_Command_Set(std::array<MotorCommand, 12> command);
    void parseSlaverMessages();
    void Slave_Data_Get(bool showInfo=false);

    void Master_Send_Cmd();
    std::array<MotorState, 12> getMotorStates(){
        // Slave_Data_Get(showInfo);

        return mMotorStates;
    }
    bool enableAllDevices();
    bool disableAllDevices();

    void setStatusWord();

    void setBatteryPowerOff(void);
    uint8_t getBatteryPoweringOff(void);
    uint8_t getBatterySOC(void);
    float getBatteryVoltage(void);
    float getBatteryCurrent(void);
    uint8_t getBatteryError(void);
    std::array<bool, 12> getMotorCommunicationState(){return mCanConnectState;}
    

    protected:
        Master_TxMsg TxMessage;

        Slave_RxMsg RxMessage;

        std::array<bool, 12> mCanConnectState{true};
        std::array<MotorState, 12> mMotorStates{};
        uint8_t mMotorId[12];
        int fd;

};


}


#pragma pack(pop)