#include <stdio.h>
#include <stdint.h>
#include <stdlib.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/ioctl.h>
#include <linux/types.h>
#include <linux/spi/spidev.h>

#include "gsmp_spi_sdk/spi-can.h"
const char *spi_device = "/dev/spidev0.0";   // 替换为实际的SPI设备节点，如 /dev/spidev0.0
const int  speed = 5000000;                  // 设置SPI时钟频率，可根据实际需求调整，单位Hz
const int  bits = 8;
const int  mode = SPI_MODE_3;


// int gsmp::spiCan::spiInit(const char *spi_device, int speed, int bits, int mode) {
int gsmp::spiCan::spiInit() {
    // mMotorId[0] = 0x0c;                    // 小米电机出厂默认为1, 见手册CAN_ID(0x200a), 最大127
    // mMotorId[1] = 0x02;                    // todo 后续修改其他电机Id
    // mMotorId[2] = 0x01;
    // mMotorId[3] = 0x05;
    // mMotorId[4] = 0x03;
    // mMotorId[5] = 0x04;
    // mMotorId[6] = 0x06;
    // mMotorId[7] = 0x08;
    // mMotorId[8] = 0x07;
    // mMotorId[9] = 0x0b;
    // mMotorId[10] = 0x09;
    // mMotorId[11] = 0x0a;
    mMotorId[0] = 0x01;                    // 小米电机出厂默认为1, 见手册CAN_ID(0x200a), 最大127
    mMotorId[1] = 0x02;                    // todo 后续修改其他电机Id
    mMotorId[2] = 0x03;
    mMotorId[3] = 0x04;
    mMotorId[4] = 0x05;
    mMotorId[5] = 0x06;
    mMotorId[6] = 0x07;
    mMotorId[7] = 0x08;
    mMotorId[8] = 0x09;
    mMotorId[9] = 0x0a;
    mMotorId[10] = 0x0b;
    mMotorId[11] = 0x0c;
    // int fd;
    // 打开SPI设备文件
    fd = open(spi_device, O_RDWR);

    printf("fd:%d \n",fd);
    printf("mode:%d \n",mode);
    if (fd < 0) {
        perror("Can't open SPI device");
        return -1;
    }

    // 设置SPI模式
    if (ioctl(fd, SPI_IOC_WR_MODE, &mode) == -1) {
        perror("Can't set SPI mode");
        close(fd);
        return -1;
    }
    // if (ioctl(fd, SPI_IOC_RD_MODE, &mode) == -1) {
    //     printf("111111");

    //     perror("Can't get SPI mode");
    //     close(fd);
    //     return -1;
    // }

    // 设置SPI位宽
    if (ioctl(fd, SPI_IOC_WR_BITS_PER_WORD, &bits) == -1) {
        perror("Can't set bits per word");
        close(fd);
        return -1;
    }
    // if (ioctl(fd, SPI_IOC_RD_BITS_PER_WORD, &bits) == -1) {
    //     perror("Can't get bits per word");
    //     close(fd);
    //     return -1;
    // }

    // 设置SPI最大时钟频率
    if (ioctl(fd, SPI_IOC_WR_MAX_SPEED_HZ, &speed) == -1) {
        perror("Can't set max speed hz");
        close(fd);
        return -1;
    }
    // if (ioctl(fd, SPI_IOC_RD_MAX_SPEED_HZ, &speed) == -1) {
    //     perror("Can't get max speed hz");
    //     close(fd);
    //     return -1;
    // }

    return fd;
}

int gsmp::spiCan::spiTransfer(Master_TxMsg *tx_data,Slave_RxMsg *rx_data) {
    uint8_t *txData = (uint8_t *)tx_data;
    uint8_t *rxData = (uint8_t *)rx_data;

    struct spi_ioc_transfer transfer = {
       .tx_buf = (unsigned long)txData,
       .rx_buf = (unsigned long)rxData,
       .len = sizeof(Master_TxMsg),
       .speed_hz = speed,
       .delay_usecs = 0,
       .bits_per_word = bits,
       .cs_change = 1,  // 设置 cs_change 为 1，表示传输过程中改变片选信号，可根据需求调整
       .tx_nbits = 0,  // 未指定发送位数，设置为 0 表示按 len 计算
       .rx_nbits = 0,  // 未指定接收位数，设置为 0 表示按 len 计算
       .word_delay_usecs = 0,  // 字之间的延迟设置为 0
       .pad = 0  // 保留字段设置为 0
    };



    if (ioctl(fd, SPI_IOC_MESSAGE(1), &transfer) < 0) {
        perror("SPI transfer failed");
        return -1;
    }

    return 0;
}

void gsmp::spiCan::Master_Command_Set(std::array<MotorCommand, 12> command)
{
    // uint8_t data = 0x11;
    uint16_t kp_int;
    uint16_t kd_int;
    uint16_t pos_int;
    uint16_t spd_int;
    uint16_t tor_int;

    uint8_t command_flag = 0x04;    // 电机运动控制模式


    for (uint8_t i = 0; i < 12; i++){
        TxMessage.motor[i].dlc = 8;
        TxMessage.motor[i].ide = 0;
        uint32_t command_id = 0;
        command_id |= static_cast<uint32_t>(command_flag) << 5;
        // command_id |= static_cast<uint32_t>(tor_int) << 8;
        command_id |= static_cast<uint32_t>(mMotorId[i]);
        TxMessage.motor[i].id = command_id;

        double kp = gsmp::saturate(command[i].kp, KP_MIN, KP_MAX);
        double kd = gsmp::saturate(command[i].kd, KD_MIN, KD_MAX);
        double pos = gsmp::saturate(command[i].des_pos, POSITION_MIN, POSITION_MAX);
        double spd = gsmp::saturate(command[i].des_vel, VELOCITY_MIN, VELOCITY_MAX);
        double tor = gsmp::saturate(command[i].tau_ff, TORQUE_MIN, TORQUE_MAX);



        kp_int = gsmp::float_to_uint(kp, KP_MIN, KP_MAX, SEND_KP_LENGTH);
        kd_int = gsmp::float_to_uint(kd, KD_MIN, KD_MAX, SEND_KD_LENGTH);
        pos_int = gsmp::float_to_uint(pos, POSITION_MIN, POSITION_MAX, SEND_POSITION_LENGTH);
        spd_int = gsmp::float_to_uint(spd, VELOCITY_MIN, VELOCITY_MAX, SEND_VELOCITY_LENGTH);
        tor_int = gsmp::float_to_uint(tor, TORQUE_MIN, TORQUE_MAX, SEND_TORQUE_LENGTH);


        TxMessage.motor[i].data[0] = pos_int;
        TxMessage.motor[i].data[1] = pos_int >> 8;
        TxMessage.motor[i].data[2] = spd_int;
        TxMessage.motor[i].data[3] = ((spd_int >> 8) & 0x3f)| ((kp_int & 0x03) << 6);
        TxMessage.motor[i].data[4] = kp_int >> 2;
        TxMessage.motor[i].data[5] = kd_int;
        TxMessage.motor[i].data[6] = tor_int;
        TxMessage.motor[i].data[7] = tor_int >> 8;

    }
}

void gsmp::spiCan::parseSlaverMessages(){
    static unsigned int canDisconnectCount[12]{0};
    static unsigned int canConnectCount[12]{0};

    for (int i = 0; i < 12; ++i) {
        uint32_t can_id = RxMessage.motor[i].id & 0x0F;
        uint32_t command_id = (RxMessage.motor[i].id >> 5) & 0x3f;


        if (RxMessage.motor[i].dlc == 0 || (can_id) != mMotorId[i]) {
            canDisconnectCount[i]++;
            if (canDisconnectCount[i] >= 30) {
                canConnectCount[i] = 0;
                if (canDisconnectCount[i] % 30 == 0) {
                    printf(" %d 号电机未响应\n\033[0m", i + 1 );
                }
                mCanConnectState[i] = false;
            }
        } else {
            if (!mCanConnectState[i]) {
                canConnectCount[i]++;
                if (canConnectCount[i] >= 10) {
                    canDisconnectCount[i] = 0;
                    printf("%d 号电机恢复响应\n\033[0m", i + 1);
                    mCanConnectState[i] = true;
                }
            }
        }
        
        uint8_t fb_state;
        switch(command_id)
        {
            case ENABLE_MOTOR:
                fb_state = RxMessage.motor[i].data[0] & 0x01;
                if(fb_state == 0)
                    printf("[INFO] Motor with id: %d enable success\r\n", (uint32_t)can_id);
                else
                    printf("[INFO] Motor with id: %d enable failed\r\n", (uint32_t)can_id);
                break;

            case DISABLE_MOTOR:
                fb_state = RxMessage.motor[i].data[0] & 0x01;
                if(fb_state == 0)
                    printf("[INFO] Motor with id: %d disenable success\r\n", (uint32_t)can_id);
                else
                    printf("[INFO] Motor with id: %d disenable failed\r\n", (uint32_t)can_id);
                break;

            case CONTROL_MOTOR:
            {

                auto pos_int =static_cast<uint32_t>(RxMessage.motor[i].data[0]);
                pos_int |= static_cast<uint32_t>(RxMessage.motor[i].data[1] << 8);
                pos_int |= static_cast<uint32_t>((RxMessage.motor[i].data[2] & 0x0f) << 16);
 
                auto vel_int = static_cast<uint32_t>(RxMessage.motor[i].data[2] >> 4);
                vel_int |= static_cast<uint32_t>(RxMessage.motor[i].data[3] << 4);
                vel_int |= static_cast<uint32_t>(RxMessage.motor[i].data[4] << 12);

                auto tor_int = static_cast<uint32_t>(RxMessage.motor[i].data[5]);
                tor_int |= static_cast<uint32_t>(RxMessage.motor[i].data[6] << 8);

                auto temp_flag = RxMessage.motor[i].data[7] & 0x01;
                auto temp_int = static_cast<uint32_t>(RxMessage.motor[i].data[7] >> 1);


                mMotorStates[i].pos = gsmp::uint_to_float(pos_int, POSITION_MIN, POSITION_MAX, RECEIVE_POSITION_LENGTH);
                mMotorStates[i].vel = gsmp::uint_to_float(vel_int, VELOCITY_MIN, VELOCITY_MAX, RECEIVE_VELOCITY_LENGTH);
                mMotorStates[i].tau = gsmp::uint_to_float(tor_int, TORQUE_MIN, TORQUE_MAX, RECEIVE_TORQUE_LENGTH);

                if(temp_flag == 1) //motor temp flag
                    mMotorStates[i].temp = gsmp::uint_to_float(temp_int, MOTOR_TEMP_MIN, MOTOR_TEMP_MAX, RECEIVE_TEMP_LENGTH);
                else // Driver Temp Flag  
                    mMotorStates[i].temp = gsmp::uint_to_float(temp_int, DRIVER_TEMP_MIN, DRIVER_TEMP_MAX, RECEIVE_TEMP_LENGTH);
                
            }

                break;

            case 23:
            {               
                auto error_code = static_cast<uint16_t>(RxMessage.motor[i].data[1]  << 8 | RxMessage.motor[i].data[0] );
                if(error_code != kMotorNoError){
                    if(error_code & kOverVoltage){
                        printf("[ERROR] Motor with id: %d kOverVoltage\r\n", (uint32_t)can_id);
                    }
                    if(error_code & kUnderVoltage){
                        printf("[ERROR] Motor with id: %d kUnderVoltage\r\n", (uint32_t)can_id);
                    }
                    if(error_code & kOverCurrent){
                        printf("[ERROR] Motor with id: %d kOverCurrent\r\n", (uint32_t)can_id);
                    }
                    if(error_code & kMotorOverTemp){
                        printf("[ERROR] Motor with id: %d kMotorOverTemp\r\n", (uint32_t)can_id);
                    }
                    if(error_code & kDriverOverTemp){
                        printf("[ERROR] Motor with id: %d kDriverOverTemp\r\n", (uint32_t)can_id);
                    }
                    if(error_code & kCanTimeout){
                        printf("[ERROR] Motor with id: %d kCanTimeout\r\n", (uint32_t)can_id);
                    }
                }
            }
                break;

            default:

                break;

        }

    }
    
}

void gsmp::spiCan::Slave_Data_Get(bool showInfo)
{
    parseSlaverMessages();
    for(uint8_t i =0;i<12;i++) 
    {    
        if(showInfo){

            printf("id: %#x ", RxMessage.motor[i].id);
            printf("dlc: %#x ", RxMessage.motor[i].dlc);
            printf("sts: %#x ", RxMessage.motor[i].sts);     
            printf("data: ");
            for(uint8_t j =0;j<RxMessage.motor[i].dlc;j++) {
                printf(" %#x", RxMessage.motor[i].data[j]);
            } 
            printf("\n");      
        }    
    }

    // printf("Vol: %.1f; ",getBatteryVoltage());
    // printf("Cur: %.1f; ",getBatteryCurrent());
    // printf("SOC: %d; ",getBatterySOC());
    // printf("Err: %d\n",getBatteryError()); 
}


bool gsmp::spiCan::enableAllDevices() {
    uint8_t command_flag = 0x02;
    // auto master_id = static_cast<uint16_t>(mCanMasterId);

    static bool firstPrint = true;

    for (int index = 0; index < 12; index++) {
        uint32_t command_id = 0;
        command_id |= static_cast<uint32_t>(command_flag) << 5;
        // command_id |= static_cast<uint32_t>(master_id) << 8;
        command_id |= static_cast<uint32_t>(mMotorId[index]);

        TxMessage.motor[index].dlc = 0;
        TxMessage.motor[index].ide = 0x00;
        TxMessage.motor[index].id = command_id;

        TxMessage.motor[index].data[0] = 0x00;
        TxMessage.motor[index].data[1] = 0x00;
        TxMessage.motor[index].data[2] = 0x00;
        TxMessage.motor[index].data[3] = 0x00;
        TxMessage.motor[index].data[4] = 0x00;
        TxMessage.motor[index].data[5] = 0x00;
        TxMessage.motor[index].data[6] = 0x00;
        TxMessage.motor[index].data[7] = 0x00;
    }
    if (firstPrint) {
        printf("CyberGear motors enable command sent!\n");
    }
    return true;
}

bool gsmp::spiCan::disableAllDevices() {
    uint8_t command_flag = 0x01;
    // auto master_id = static_cast<uint16_t>(mCanMasterId);

    static bool firstPrint = true;

    for (int index = 0; index < 12; index++) {
        uint32_t command_id = 0;
        command_id |= static_cast<uint32_t>(command_flag) << 5;
        // command_id |= static_cast<uint32_t>(master_id) << 8;
        command_id |= static_cast<uint32_t>(mMotorId[index]);

        TxMessage.motor[index].sts = 0x00;
        TxMessage.motor[index].rtr = 0x00;
        TxMessage.motor[index].ide = 0x00;
        TxMessage.motor[index].id = command_id;
        TxMessage.motor[index].dlc = 0;

        TxMessage.motor[index].data[0] = 0x00;
        TxMessage.motor[index].data[1] = 0x00;
        TxMessage.motor[index].data[2] = 0x00;
        TxMessage.motor[index].data[3] = 0x00;
        TxMessage.motor[index].data[4] = 0x00;
        TxMessage.motor[index].data[5] = 0x00;
        TxMessage.motor[index].data[6] = 0x00;
        TxMessage.motor[index].data[7] = 0x00;
    }

    if (firstPrint) {
        printf("CyberGear motors disable command sent!\n");
    }
    return true;
}


void gsmp::spiCan::setStatusWord(){
    
    uint8_t command_flag = 0x17;
    // auto master_id = static_cast<uint16_t>(mCanMasterId);

    // static bool firstPrint = true;

    for (int index = 0; index < 12; index++) {
        uint32_t command_id = 0;
        command_id |= static_cast<uint32_t>(command_flag) << 5;
        // command_id |= static_cast<uint32_t>(master_id) << 8;
        command_id |= static_cast<uint32_t>(mMotorId[index]);

        TxMessage.motor[index].sts = 0x00;
        TxMessage.motor[index].rtr = 0x00;
        TxMessage.motor[index].ide = 0x00;
        TxMessage.motor[index].id = command_id;
        TxMessage.motor[index].dlc = 0;

        TxMessage.motor[index].data[0] = 0x00;
        TxMessage.motor[index].data[1] = 0x00;
        TxMessage.motor[index].data[2] = 0x00;
        TxMessage.motor[index].data[3] = 0x00;
        TxMessage.motor[index].data[4] = 0x00;
        TxMessage.motor[index].data[5] = 0x00;
        TxMessage.motor[index].data[6] = 0x00;
        TxMessage.motor[index].data[7] = 0x00;
    }


}


void gsmp::spiCan::Master_Send_Cmd()
{
    if (spiTransfer(&TxMessage,&RxMessage) < 0) {
        printf("SPI transfer failed");
        close(fd);
        // return -1;
    }
    // else{
        // printf("SPI transfer sussced\n");
    // }

}



// int main() {
//     int fd = spiInit(spi_device, speed, bits, mode);
//     if (fd < 0) {
//         printf("SPI initial failed\n");
//         return -1;
//     }else{
//         printf("SPI initialed\n");
//     }

//  while (1)
//  {
//     Master_Command_Set();

//     if (spiTransfer(fd,&TxMessage,&RxMessage) < 0) {
//         printf("SPI transfer failed");
//         close(fd);
//         return -1;
//     }else{
//         printf("SPI transfer sussced\n");
//     }

//     Slave_Data_Get();

//     usleep(2000);
// }

//     close(fd);
//     return 0;
// }

/** set battery power off command */
void gsmp::spiCan::setBatteryPowerOff(void){
   static uint8_t txFlag = 1;        //发送1次
   if(txFlag){
      TxMessage.bms.id  = 0x200;
      TxMessage.bms.dlc = 0x02;
      TxMessage.bms.data[0] = 0x01;

      txFlag = 0;
   }
}

/** get battery powering off feedback */
uint8_t gsmp::spiCan::getBatteryPoweringOff(void){
   uint8_t ret = 0;
   
   if ((RxMessage.bms.data[5]&0x0F) == 0x4){
      ret = 1;
   }
   
   return ret;
}

uint8_t gsmp::spiCan::getBatterySOC(void){
   return RxMessage.bms.data[3];
}

float gsmp::spiCan::getBatteryVoltage(void){
   uint16_t vol;
   
   vol = (uint16_t)(RxMessage.bms.data[0])<<2;
   vol |= (RxMessage.bms.data[1]>>6);

   return vol*0.1f;
}

float gsmp::spiCan::getBatteryCurrent(void){
   uint16_t cur;
   
   cur = (uint16_t)(RxMessage.bms.data[1]&0x0F)<<8;
   cur |= RxMessage.bms.data[2];

   return cur*0.1f-100.f;
}

uint8_t gsmp::spiCan::getBatteryError(void){
   uint8_t err = 0;

   if(RxMessage.bms.data[5]>>7 ){
      err = 1;
      printf("battery pack over voltage\n");       
   }

   if(RxMessage.bms.data[6] & 0x01 ){
      err = 2;
      printf("battery pack under voltage\n");       
   }

   if((RxMessage.bms.data[6]>>1) & 0x01 ){
      err = 3;
      printf("battery cell over voltage\n");       
   }  

   if((RxMessage.bms.data[6]>>2) & 0x01 ){
      err = 4;
      printf("battery cell under voltage\n");       
   }

   if((RxMessage.bms.data[6]>>3) & 0x01 ){
      err = 5;
      printf("battery discharge over current\n");       
   }

   if((RxMessage.bms.data[6]>>4) & 0x01 ){
      err = 6;
      printf("battery charge over current\n");       
   }

   if((RxMessage.bms.data[6]>>5) & 0x01 ){
      err = 7;
      printf("battery output short circuit\n");       
   }

   if((RxMessage.bms.data[6]>>6) & 0x01 ){
      err = 8;
      printf("battery pack over temperature\n");       
   } 

   if((RxMessage.bms.data[6]>>7) & 0x01 ){
      err = 9;
      printf("battery pack under temperature\n");       
   }     

   return err;                
}