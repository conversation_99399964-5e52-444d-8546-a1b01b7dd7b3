# Generated on Sat Feb  9 13:18:45 2019
__collection-name__: robot-parameters

controller_dt     : 0.002
control_mode      : 0
myValue           : 2222
#stand_kd_cartesian: [11,11,11]
#stand_kp_cartesian: [700, 700, 150]
stand_kd_cartesian: [2.5,2.5,2.5]
stand_kp_cartesian: [50, 50, 50]
#stand_kd_cartesian: [0.0, 0.0, 0.0]
#stand_kp_cartesian: [0,0,0]
testValue         : 23
cheater_mode      : 0
#cheater_mode      : 1
foot_height_sensor_noise      :  0.001
foot_process_noise_position   :  0.002
foot_sensor_noise_position    :  0.001
#foot_sensor_noise_velocity    :  0.1
foot_sensor_noise_velocity    :  0.05
imu_process_noise_position    :  0.02
imu_process_noise_velocity    :  0.02
kpCOM: [50,50,50]
kdCOM: [10,10,10]
kpBase: [300,200,100]
kdBase: [20,10,10]
use_rc: 1
#use_rc: 0
