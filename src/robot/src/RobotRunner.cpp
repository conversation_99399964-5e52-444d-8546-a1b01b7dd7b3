/*!
 * @file RobotRunner.cpp
 * @brief Common framework for running robot controllers.
 * This code is a common interface between control code and hardware/simulation
 * for mini cheetah and cheetah 3
 */

#include <unistd.h>

#include "RobotRunner.h"
#include "Controllers/ContactEstimator.h"
#include "Controllers/OrientationEstimator.h"
#include "Dynamics/Cheetah3.h"
#include "Dynamics/MiniCheetah.h"
#include "Utilities/Utilities_print.h"
#include "ParamHandler.hpp"
#include "Utilities/Timer.h"
#include "Controllers/PositionVelocityEstimator.h"
//#include "rt/rt_interface_lcm.h"

RobotRunner::RobotRunner(RobotController* robot_ctrl, 
    PeriodicTaskManager* manager, 
    float period, std::string name):
  PeriodicTask(manager, period, name),
  _lcm(getLcmUrl(255)) {

    _robot_ctrl = robot_ctrl;
  }

/**
 * Initializes the robot model, state estimator, leg controller,
 * robot data, and any control logic specific data.
 */
void RobotRunner::init() {
  printf("[RobotRunner] initialize\n");

  // Build the appropriate Quadruped object
  if (robotType == RobotType::MINI_CHEETAH) {
    _quadruped = buildMiniCheetah<float>();
  } else {
    _quadruped = buildCheetah3<float>();
  }

  // Initialize the model and robot data
  _model = _quadruped.buildModel();
  _jpos_initializer = new JPosInitializer<float>(3., controlParameters->controller_dt);

  // Always initialize the leg controller and state entimator
  _legController = new LegController<float>(_quadruped);
  _stateEstimator = new StateEstimatorContainer<float>(
      cheaterState, vectorNavData, _legController->datas,
      &_stateEstimate, controlParameters);
  _t265stateEstimator = new StateEstimatorContainer<float>(
            cheaterState, vectorNavData, _legController->datas,
            &_t265stateEstimate, controlParameters);
  initializeStateEstimator(false);

  memset(&rc_control, 0, sizeof(rc_control_settings));
  // Initialize the DesiredStateCommand object
  _desiredStateCommand =
    new DesiredStateCommand<float>(driverCommand,
        &rc_control,
        controlParameters,
        &_stateEstimate,
        controlParameters->controller_dt);

  // Controller initializations
  _robot_ctrl->_model = &_model;
  _robot_ctrl->_quadruped = &_quadruped;
  _robot_ctrl->_legController = _legController;
  _robot_ctrl->_stateEstimator = _stateEstimator;
  _robot_ctrl->_t265stateEstimator = _t265stateEstimator; //添加t265
  _robot_ctrl->_stateEstimate = &_stateEstimate;
  _robot_ctrl->_visualizationData= visualizationData;
  _robot_ctrl->_robotType = robotType;
  _robot_ctrl->_driverCommand = driverCommand;
  _robot_ctrl->_controlParameters = controlParameters;
  _robot_ctrl->_desiredStateCommand = _desiredStateCommand;

  _robot_ctrl->initializeController();

  _legController->initpub();
  #ifdef REAL_ROBOT
  _legController->init();
  std::cout << "robotrunner----" << "init---" << std::endl;
  #endif

}

/**
 * Runs the overall robot control system by calling each of the major components
 * to run each of their respective steps.
 *
 */
void RobotRunner::run() {
//    clock_gettime(CLOCK_MONOTONIC, &now); //当前到时间
//   std::cout<<"timer"<< ((int64_t)(now.tv_nsec - _startTime.tv_nsec) + 1000000000 * (now.tv_sec - _startTime.tv_sec))/ 1.e6<<std::endl;
    // std::cout << "robotrunner----" << "runuing---" << std::endl;
  // Run the state estimator step
  //_stateEstimator->run(cheetahMainVisualization);

  _stateEstimator->run();
  //cheetahMainVisualization->p = _stateEstimate.position;
  visualizationData->clear();

  // Update the data from the robot
  setupStep();

  static int count_ini(0);
  ++count_ini;
  if (count_ini < 10) {
    _legController->setEnabled(false);
  } else if (20 < count_ini && count_ini < 30) {
    _legController->setEnabled(false);
  } else if (40 < count_ini && count_ini < 50) {
    _legController->setEnabled(false);
  } else {
    _legController->setEnabled(true);

    if( (rc_control.mode == 0) && controlParameters->use_rc ) {
      if(count_ini%1000 ==0)   printf("ESTOP!\n");

      for (int leg = 0; leg < 4; leg++) {
        Vec3<float> qDes(
            -base_motor[leg*3 + 0] * direction_motor[leg*3 + 0],  // abad 关节
            -base_motor[leg*3 + 1] * direction_motor[leg*3 + 1],  // hip 关节
            -base_motor[leg*3 + 2] * direction_motor[leg*3 + 2]   // knee 关节
        );
        _legController->commands[leg].zero();
        _legController->commands[leg].qDes = qDes;
      }  

      _robot_ctrl->Estop();  //设置FSM_OperatingMode::NORMAL

    }else {
      // Controller
      if (0)// !_jpos_initializer->IsInitialized(_legController))
          {
        Mat3<float> kpMat;
        Mat3<float> kdMat;
        // Update the jpos feedback gains
        if (robotType == RobotType::MINI_CHEETAH) {
          kpMat << 5, 0, 0, 0, 5, 0, 0, 0, 5;
          kdMat << 0.1, 0, 0, 0, 0.1, 0, 0, 0, 0.1;
        } else if (robotType == RobotType::CHEETAH_3) {
          kpMat << 50, 0, 0, 0, 50, 0, 0, 0, 50;
          kdMat << 1, 0, 0, 0, 1, 0, 0, 0, 1;
        } else {
          assert(false);
        } 

        for (int leg = 0; leg < 4; leg++) {
          _legController->commands[leg].kpJoint = kpMat;
          _legController->commands[leg].kdJoint = kdMat;
        }

      } else {
        // Run Control 
        _robot_ctrl->runController();
        cheetahMainVisualization->p = _stateEstimate.position;

        // Update Visualization
        _robot_ctrl->updateVisualization();
        cheetahMainVisualization->p = _stateEstimate.position;

      }
    }

  }

  // Visualization (will make this into a separate function later)
  for (int leg = 0; leg < 4; leg++) {
    for (int joint = 0; joint < 3; joint++) {
      cheetahMainVisualization->q[leg * 3 + joint] = _legController->datas[leg].q[joint];
    }
  }
    cheetahMainVisualization->p.setZero();
    cheetahMainVisualization->p = _stateEstimate.position;
    cheetahMainVisualization->quat = _stateEstimate.orientation;

  // Sets the leg controller commands for the robot appropriate commands
  finalizeStep();

//
//  clock_gettime(CLOCK_MONOTONIC, &_startTime);
//  static long time_c=0;
//  time_c++;
//  if (time_c%50==0)
//  std::cout<<"timer  "<< ((int64_t)(_startTime.tv_nsec - now.tv_nsec) + 1000000000 * (_startTime.tv_sec - now.tv_sec))/ 1.e6<<std::endl;
////

}

/*!
 * Before running user code, setup the leg control and estimators
 */
void RobotRunner::setupStep() {
  // Update the leg data
  if (robotType == RobotType::MINI_CHEETAH) {

    #ifdef SIMULATION
    _legController->updateData(spiData);
    #endif

    #ifdef REAL_ROBOT
    _legController->updateData();
    #endif

  } else if (robotType == RobotType::CHEETAH_3) {
    _legController->updateData(tiBoardData);
  } else {
    assert(false);
  }

  // Setup the leg controller for a new iteration
  _legController->zeroCommand();
  _legController->setEnabled(true);
  _legController->setMaxTorqueCheetah3(208.5);

  // state estimator
  // check transition to cheater mode:
  if (!_cheaterModeEnabled && controlParameters->cheater_mode) {
    printf("[RobotRunner] Transitioning to Cheater Mode...\n");
    initializeStateEstimator(true);
    // todo any configuration
    _cheaterModeEnabled = true;
  }

  // check transition from cheater mode:
  if (_cheaterModeEnabled && !controlParameters->cheater_mode) {
    printf("[RobotRunner] Transitioning from Cheater Mode...\n");
    initializeStateEstimator(false);
    // todo any configuration
    _cheaterModeEnabled = false;
  }

  get_rc_control_settings(&rc_control);

  // if(rc_control.mode) printf("[RobotRunner] control mode: %f",rc_control.mode);

  // todo safety checks, sanity checks, etc...
}

/*!
 * After the user code, send leg commands, update state estimate, and publish debug data
 */
void RobotRunner::finalizeStep() {
  // 简单的周期测量和输出（通过环境变量SHOW_MOTOR_PERIOD=1启用）
  static bool enablePeriodMonitor = (getenv("SHOW_MOTOR_PERIOD") != nullptr);
  static struct timespec lastTime = {0, 0};
  static bool firstCall = true;
  static int callCount = 0;

  if (enablePeriodMonitor) {
    struct timespec currentTime;
    clock_gettime(CLOCK_MONOTONIC, &currentTime);

    if (!firstCall) {
      double periodMs = (currentTime.tv_sec - lastTime.tv_sec) * 1000.0 +
                        (currentTime.tv_nsec - lastTime.tv_nsec) / 1000000.0;
      double frequency = 1000.0 / periodMs;

      callCount++;
      // 每500次打印一次（约1秒）
      if (callCount % 500 == 0) {
        printf("[电机命令周期] %.3f ms, %.1f Hz\n", periodMs, frequency);
      }
    } else {
      firstCall = false;
      printf("[电机命令周期] 开始监控...\n");
    }

    lastTime = currentTime;
  }

  if (robotType == RobotType::MINI_CHEETAH) {

  #ifdef SIMULATION
    if(emergency_flag) _legController->emergencyStop();
    else _legController->updateCommand(spiCommand);
  #endif

  #ifdef REAL_ROBOT
    if(emergency_flag) _legController->emergencyStop();
    else _legController->updateCommand();
  #endif

  } else if (robotType == RobotType::CHEETAH_3) {
    _legController->updateCommand(tiBoardCommand);
  } else {
    assert(false);
  }
  _legController->setLcm(&leg_control_data_lcm, &leg_control_command_lcm);
  _stateEstimate.setLcm(state_estimator_lcm);
  _lcm.publish("leg_control_command", &leg_control_command_lcm);
  _lcm.publish("leg_control_data", &leg_control_data_lcm);
  _lcm.publish("state_estimator", &state_estimator_lcm);

    global_to_robot_lcmt.rpy[0] = state_estimator_lcm.rpy[0];
    global_to_robot_lcmt.rpy[1] = state_estimator_lcm.rpy[1];
    global_to_robot_lcmt.rpy[2] = state_estimator_lcm.rpy[2];
    global_to_robot_lcmt.xyz[0] = state_estimator_lcm.p[0];
    global_to_robot_lcmt.xyz[1] = state_estimator_lcm.p[1];
    global_to_robot_lcmt.xyz[2] = state_estimator_lcm.p[2];
  _lcm.publish("global_to_robot", &global_to_robot_lcmt);
  _iterations++;
}

/*!
 * Reset the state estimator in the given mode.
 * @param cheaterMode
 */
void RobotRunner::initializeStateEstimator(bool cheaterMode) {
  _stateEstimator->removeAllEstimators();
  _stateEstimator->addEstimator<ContactEstimator<float>>();
  Vec4<float> contactDefault;
  contactDefault << 0.5, 0.5, 0.5, 0.5;
  _stateEstimator->setContactPhase(contactDefault);
  if (cheaterMode) {
    _stateEstimator->addEstimator<CheaterOrientationEstimator<float>>();
    _stateEstimator->addEstimator<CheaterPositionVelocityEstimator<float>>();
  } else {
    _stateEstimator->addEstimator<VectorNavOrientationEstimator<float>>();
    _stateEstimator->addEstimator<LinearKFPositionVelocityEstimator<float>>();
  }
}

RobotRunner::~RobotRunner() {
  delete _legController;
  delete _stateEstimator;
  delete _jpos_initializer;
}

void RobotRunner::cleanup() {}
