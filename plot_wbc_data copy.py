import lcm
import matplotlib.pyplot as plt
import numpy as np
import time
import csv
from datetime import datetime
import sys
import os
import signal

# 运行一定时间后自动退出并保存数据
#在运行前进入conda环境
# conda activate 
#export PYTHONPATH=/home/<USER>/miniconda3/lib/python3.13/site-packages:$PYTHONPATH




# 直接导入LCM类型（假设类型文件在相同目录）
# sys.path.append(os.path.dirname(os.path.abspath(__file__))+"src/lcm-types")
sys.path.append("/home/<USER>/Desktop/03_mitcontrol_latest/src/lcm-types")
try:
    from wbc_test_data_t import wbc_test_data_t
except ImportError:
    print("Error: Could not import wbc_test_data_t. Make sure the LCM type is generated.")
    sys.exit(1)
    # 动态加载模块
# module_name = "wbc_test_data_t"
# file_path = "src/lcm-types/wbc_test_data_t.py"

# spec = importlib.util.spec_from_file_location(module_name, file_path)
# module = importlib.util.module_from_spec(spec)
# sys.modules[module_name] = module
# spec.loader.exec_module(module)

# # 现在可以通过模块引用 wbc_test_data_t
# wbc_test_data_t = module.wbc_test_data_t

# 创建数据存储结构
class DataStorage:
    def __init__(self):
        self.timestamps = []
        self.start_time = time.time()
        self.data_fields = {
            'body_pos_cmd': [],
            'body_vel_cmd': [],
            'body_ang_vel_cmd': [],
            'body_pos': [],
            'body_vel': [],
            'body_ori': [],
            'body_ang_vel': []
        }
    
    def add_data(self, wbc_data):
        current_time = time.time() - self.start_time
        self.timestamps.append(current_time)
        
        for field in self.data_fields.keys():
            if hasattr(wbc_data, field):
                self.data_fields[field].append(getattr(wbc_data, field))
    
    def save_to_csv(self, filename=None):
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"wbc_data_{timestamp}.csv"
        
        with open(filename, 'w', newline='') as f:
            writer = csv.writer(f)
            headers = ['Time']
            for field in self.data_fields.keys():
                dim_count = len(self.data_fields[field][0]) if self.data_fields[field] else 0
                dim_labels = ['X', 'Y', 'Z', 'W'][:dim_count]
                headers.extend([f"{field}_{dim}" for dim in dim_labels])
            writer.writerow(headers)
            
            for i, t in enumerate(self.timestamps):
                row = [t]
                for field in self.data_fields.keys():
                    if i < len(self.data_fields[field]):
                        row.extend(self.data_fields[field][i])
                writer.writerow(row)
        
        print(f"Data saved to {filename}")
        return filename

# 初始化数据存储
data_storage = DataStorage()

# LCM回调函数
def handle_wbc_lcm_data(channel, data):
    try:
        wbc_data = wbc_test_data_t.decode(data)
        data_storage.add_data(wbc_data)
        
        if len(data_storage.timestamps) % 100 == 0:
            print(f"Received {len(data_storage.timestamps)} messages...")
            
    except Exception as e:
        print(f"Error processing message: {e}")

# 主程序
def main():
    lc = lcm.LCM()
    subscription = lc.subscribe("wbc_lcm_data", handle_wbc_lcm_data)
    
    print("Listening for messages on wbc_lcm_data channel...")
    print("Collecting data for 5 seconds...")
    
    # 设置5秒收集时间
    start_time = time.time()
    collection_duration = 10  # 5秒
    
    try:
        while time.time() - start_time < collection_duration:
            # 设置超时时间为100ms，这样我们可以定期检查时间
            lc.handle_timeout(100)  # 毫秒
    except Exception as e:
        print(f"Error during data collection: {e}")
    
    print(f"\nData collection complete. Collected {len(data_storage.timestamps)} messages in {collection_duration} seconds.")
    
    if len(data_storage.timestamps) == 0:
        print("No data received. Exiting...")
        return
    
    # 保存数据
    csv_filename = data_storage.save_to_csv()
    
    # 创建图表
    fig, axs = plt.subplots(4, 1, figsize=(12, 16), sharex=True)
    plt.subplots_adjust(hspace=0.4)
    t = np.array(data_storage.timestamps)
    
    # 1. 位置对比
    pos_cmd = np.array(data_storage.data_fields['body_pos_cmd'])
    pos_act = np.array(data_storage.data_fields['body_pos'])
    for i in range(3):
        axs[0].plot(t, pos_cmd[:, i], '--', label=f'Cmd {["X","Y","Z"][i]}', alpha=0.7)
        axs[0].plot(t, pos_act[:, i], '-', label=f'Actual {["X","Y","Z"][i]}')
    axs[0].set_title('Body Position')
    axs[0].set_ylabel('Position (m)')
    axs[0].grid(True)
    axs[0].legend(ncol=3)
    
    # 2. 速度对比
    vel_cmd = np.array(data_storage.data_fields['body_vel_cmd'])
    vel_act = np.array(data_storage.data_fields['body_vel'])
    for i in range(3):
        axs[1].plot(t, vel_cmd[:, i], '--', label=f'Cmd {["X","Y","Z"][i]}', alpha=0.7)
        axs[1].plot(t, vel_act[:, i], '-', label=f'Actual {["X","Y","Z"][i]}')
    axs[1].set_title('Body Velocity')
    axs[1].set_ylabel('Velocity (m/s)')
    axs[1].grid(True)
    axs[1].legend(ncol=3)
    
    # 3. 角速度对比
    ang_vel_cmd = np.array(data_storage.data_fields['body_ang_vel_cmd'])
    ang_vel_act = np.array(data_storage.data_fields['body_ang_vel'])
    for i in range(3):
        axs[2].plot(t, ang_vel_cmd[:, i], '--', label=f'Cmd {["X","Y","Z"][i]}', alpha=0.7)
        axs[2].plot(t, ang_vel_act[:, i], '-', label=f'Actual {["X","Y","Z"][i]}')
    axs[2].set_title('Body Angular Velocity')
    axs[2].set_ylabel('Ang Vel (rad/s)')
    axs[2].grid(True)
    axs[2].legend(ncol=3)
    
    # 4. 朝向
    ori = np.array(data_storage.data_fields['body_ori'])
    for i, label in enumerate(['X', 'Y', 'Z', 'W']):
        if i < ori.shape[1]:  # 确保不超过维度
            axs[3].plot(t, ori[:, i], '-', label=f'Orientation {label}')
    axs[3].set_title('Body Orientation')
    axs[3].set_ylabel('Value')
    axs[3].set_xlabel('Time (s)')
    axs[3].grid(True)
    axs[3].legend(ncol=2)
    
    # 设置X轴范围
    min_time = max(0, t[0] - 0.1)
    max_time = min(t[-1] + 0.1, collection_duration)
    for ax in axs:
        ax.set_xlim(min_time, max_time)
    
    # 保存和显示图表
    plot_filename = csv_filename.replace('.csv', '.png')
    plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
    print(f"Plot saved to {plot_filename}")
    plt.show()
    
    # 清理资源
    lc.unsubscribe(subscription)

if __name__ == "__main__":
    main()