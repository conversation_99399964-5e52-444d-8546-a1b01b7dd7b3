#include <pthread.h>
#include <rt/rt_rc_interface.h>
#include <algorithm>  // 添加 clamp 函数所需头文件
#include "Utilities/EdgeTrigger.h"
#include <string.h> // memcpy
#include <stdio.h>
#include <rt/rt_sbus.h>
#include "../../../../robot/include/main_helper.h"
static pthread_mutex_t lcm_get_set_mutex =
PTHREAD_MUTEX_INITIALIZER; /**< mutex to protect gui settings coming over
                             LCM */

// Controller Settings
rc_control_settings rc_control;

/* ------------------------- HANDLERS ------------------------- */

// Controller Settings
void get_rc_control_settings(void *settings) {
  pthread_mutex_lock(&lcm_get_set_mutex);
  v_memcpy(settings, &rc_control, sizeof(rc_control_settings));
  pthread_mutex_unlock(&lcm_get_set_mutex);
}

//void get_rc_channels(void *settings) {
//pthread_mutex_lock(&lcm_get_set_mutex);
//v_memcpy(settings, &rc_channels, sizeof(rc_channels));
//pthread_mutex_unlock(&lcm_get_set_mutex);
//}

EdgeTrigger<int> mode_edge_trigger(0);
EdgeTrigger<TaranisSwitchState> backflip_prep_edge_trigger(SWITCH_UP);
EdgeTrigger<TaranisSwitchState> experiment_prep_edge_trigger(SWITCH_UP);
TaranisSwitchState initial_mode_go_switch = SWITCH_DOWN;


int init_f710_ros() {
    // ros::NodeHandle nh;
    // // ros::Subscriber joysub = nh.subscribe("/joy", 10, joy_callback);
    // ROS_INFO("[F710] Subscribed to /joy topic");
    return 0;  // 返回无意义的文件描述符（兼容原有接口）
}

void joy_callback(sensor_msgs::Joy msg) {
    F710_data data;
    
    std::cout << "joystick recieved" << std::endl;
    // 摇杆和扳机轴数据（根据实际手柄映射调整索引）
    data.left_stick_x  = msg.axes[1];  // 左摇杆 X
    data.left_stick_y  = msg.axes[0];  // 左摇杆 Y
    data.right_stick_x = msg.axes[4];  // 右摇杆 X
    data.right_stick_y = msg.axes[3];  // 右摇杆 Y
    data.left_trigger  = (msg.axes[4] + 1.0) / 2.0;  // LT [0,1]
    data.right_trigger = (msg.axes[5] + 1.0) / 2.0;  // RT [0,1]

    // 按钮数据（索引需通过 `rostopic echo /joy` 确认）
    data.button_A     = msg.buttons[0];  // A
    data.button_B     = msg.buttons[1];  // B
    data.button_X     = msg.buttons[2];  // X
    data.button_Y     = msg.buttons[3];  // Y
    data.button_LB    = msg.buttons[4];  // LB
    data.button_RB    = msg.buttons[5];  // RB
    data.button_back  = msg.buttons[6];  // BACK
    data.button_start = msg.buttons[7];  // START
    data.button_guide = msg.buttons[8];  // LOGITECH
    data.button_L3    = msg.buttons[9];  // 左摇杆按下
    data.button_R3    = msg.buttons[10]; // 右摇杆按下

    // D-Pad（可能映射为轴或按钮，需根据实际调整）
    data.dpad_up    = (msg.axes[7] > 0.5);
    data.dpad_down  = (msg.axes[7] < -0.5);
    data.dpad_left  = (msg.axes[6] < -0.5);
    data.dpad_right = (msg.axes[6] > 0.5);

    // 调用原有的控制逻辑
    update_f710(&data);
}


// 删除自定义的 deadband lambda，使用已有的全局函数
void update_f710(F710_data* data) {
    static bool last_button_back  = false;
    static bool last_button_start = false;
    static bool last_button_A     = false;
    static bool last_button_LB    = false;
    static bool last_button_X     = false;
    static bool last_button_Y     = false;
    static bool last_button_RB     = false;

    // 速度缩放系数
    float v_scale = 1.5f;
    float w_scale = 2.0f * v_scale;

    // 使用全局 deadband 函数（参数需匹配）
    data->left_stick_x  = deadband(data->left_stick_x, 0.1f, -1.0f, 1.0f);
    data->left_stick_y  = deadband(data->left_stick_y, 0.1f, -1.0f, 1.0f);
    data->right_stick_x = deadband(data->right_stick_x, 0.1f, -1.0f, 1.0f);
    data->right_stick_y = deadband(data->right_stick_y, 0.1f, -1.0f, 1.0f);

    // 模式切换逻辑（边缘触发）
    bool mode_changed = false;
    auto step_height=1.0;
    
    // 急停（最高优先级）
    if (data->button_back && !last_button_back) {
        rc_control.mode = RC_mode::OFF;
        mode_changed = true;
    } 
    // 恢复站立
    else if (data->button_start && !last_button_start) {
        rc_control.mode = RC_mode::RECOVERY_STAND;
        mode_changed = true;
        std::cout << "----------RECOVERY_STAND-------------" << std::endl;
    }
    // down
    else if (data->button_X && !last_button_X) {
        rc_control.mode = RC_mode::LIE_DOWN;
        mode_changed = true;
        std::cout << "----------LIE_DOWN-------------" << std::endl;
    }
    // 运动模式（A 按钮）
    else if (data->button_A && !last_button_A) {
        rc_control.mode = RC_mode::LOCOMOTION;
        mode_changed = true;
        std::cout << "----------LOCOMOTION-------------" << std::endl;
    }
    // 姿态调整（LB 按钮）
    else if (data->button_LB && !last_button_LB) {
        rc_control.mode = RC_mode::QP_STAND;
        mode_changed = true;
        std::cout << "----------QP_STAND-------------" << std::endl;
    }
        // 后空翻（Y 按钮）
    else if (data->button_Y  && !last_button_Y ) {
        rc_control.mode = RC_mode::BACKFLIP;
        mode_changed = true;
        std::cout << "----------BACKFLIP-------------" << std::endl;
    }
    // 后空翻（LB 按钮）
    else if (data->button_RB  && !last_button_RB ) {
        rc_control.mode = RC_mode::FRONT_JUMP;
        mode_changed = true;
        std::cout << "----------FRONT_JUMP-------------" << std::endl;
    }
    // 更新按钮历史状态
    last_button_back  = data->button_back;
    last_button_start = data->button_start;
    last_button_A     = data->button_A;
    last_button_LB    = data->button_LB;
    last_button_X     = data->button_X;

    // 模式切换后跳过实时控制
    if (mode_changed) return;

    // 将 switch 改为 if-else（兼容 enum class）
    if (rc_control.mode == RC_mode::LOCOMOTION) {
        // 步态选择（D-Pad 映射）
        static int gait_id = 3; // 默认slow trot
        if (data->dpad_up)        gait_id = 4; //standing
        else if (data->dpad_down) gait_id = 2; //walking
        else if (data->dpad_left) gait_id = 3; //slow trot
        else if (data->dpad_right) gait_id = 102; //fast trot
        rc_control.variable[0] = gait_id;

        // 导航模式（RB 按钮）
        //rc_control.variable[3] = data->button_RB ? 1 : 0;

        // 速度指令（与 AT9S 方向一致）
    rc_control.v_des[0] = v_scale * data->left_stick_x;  // 前后
    rc_control.v_des[1] = -v_scale * data->left_stick_y; // 左右
    rc_control.v_des[2] = 0;

    rc_control.omega_des[0] = 0;
    rc_control.omega_des[1] = 0;
    rc_control.omega_des[2] = w_scale * data->right_stick_y; // 转向

    rc_control.step_height=step_height;
    // 高度调整（限制范围）
    rc_control.height_variation = std::clamp(data->right_stick_x, -0.05f, 0.1f);
    }
    else if (rc_control.mode == RC_mode::QP_STAND) {
        // 姿态调整
        rc_control.rpy_des[0] = data->left_stick_x;  // 横滚
        rc_control.rpy_des[1] = data->left_stick_y;  // 俯仰
        rc_control.height_variation = data->right_stick_y; // 高度
    }
    // 其他模式无需处理
}

void sbus_packet_complete_at9s(){
    AT9s_data data;
    update_taranis_at9s(&data);
    float v_scale = 1.5;
    float w_scale=2*v_scale;

    auto estop_switch = data.SWE;
    auto QP_Locomotion_switch =data.SWA;
    //auto Locomotion_switch=data.SWB;

    auto left_select=data.SWC;
    auto right_select=data.SWD;

    auto normal_jump_flip_switch=data.SWG;
    auto roll_show=0.0;//data.varB*1.1;
    auto step_height=data.varB+1.0;
    auto selected_navigation=data.SWF;
    int  selected_mode = 0;
//printf("at9s in\n");
    switch(estop_switch) {
        case AT9S_TRI_UP:
            selected_mode = RC_mode::OFF;
            // if(selected_navigation == 1)
            // {
            //   printf("buf_socket[0]:%.3f\n", buf_socket_data[0]);
            //   printf("buf_socket[1]:%.3f\n", buf_socket_data[1]);
            //   printf("buf_socket[2]:%.3f\n", buf_socket_data[2]);
            // }
            // else
            // printf("wangchong:%.3f\n", buf_socket_data[2]);
              
            break;

        case AT9S_TRI_MIDDLE:
            selected_mode = RC_mode::RECOVERY_STAND;
            break;

        case AT9S_TRI_DOWN:
            //selected_mode = RC_mode::LOCOMOTION;
            if(normal_jump_flip_switch==AT9S_TRI_MIDDLE)
            {
                if(QP_Locomotion_switch==AT9S_BOOL_UP)
                {
                    selected_mode = RC_mode::LOCOMOTION;
                    // Deadband
                    data.left_stick_x = deadband(data.left_stick_x, 0.1, -1., 1.);
                    data.left_stick_y = deadband(data.left_stick_y, 0.1, -1., 1.);
                    data.right_stick_x = deadband(data.right_stick_x, 0.1, -1., 1.);
                    data.right_stick_y = deadband(data.right_stick_y, 0.1, -1., 1.);

                    int gait_id=9;
                    if(right_select==AT9S_BOOL_UP)
                    {
                        if (left_select==AT9S_TRI_UP)
                            gait_id=9; // trot
                        else if(left_select==AT9S_TRI_MIDDLE)
                            gait_id=3;// slow trot
                        else if(left_select==AT9S_TRI_DOWN) //walk
                            gait_id=6;
                    }else if(right_select==AT9S_BOOL_DOWN)
                    {
                        if (left_select==AT9S_TRI_UP)
                            gait_id=5; // flying trot
                        else if(left_select==AT9S_TRI_MIDDLE)
                            gait_id=1;// bound
                        else if(left_select==AT9S_TRI_DOWN)
                            gait_id=2; // pronk
                    }
                    
                    
                    rc_control.variable[0] =gait_id;
                    rc_control.variable[3] =selected_navigation;
                    if(selected_navigation == 1)
                    {
                    rc_control.v_des[0] =  buf_socket_data[2] >0? v_scale * buf_socket_data[2]: buf_socket_data[2]/2.0;
                    rc_control.omega_des[2] = buf_socket_data[1];
                    printf("flag:%.1f\n",  buf_socket_data[0]);
                    printf("navation_Vx_rate:%.3f\n", rc_control.v_des[0]);
                    printf("navation_turn_rate:%.3f\n", rc_control.omega_des[2]);
                    printf("buf_socket[1]:%.3f\n", buf_socket_data[1]);
                    printf("buf_socket[2]:%.3f\n", buf_socket_data[2]);
                    
                    }
                    else
                    {
                    rc_control.v_des[0] = data.right_stick_x>0? v_scale * data.right_stick_x: v_scale/2.0 * data.right_stick_x;
                    rc_control.omega_des[2] = w_scale * data.left_stick_y;
                    
                    printf("RC_Vx_rate:%.3f\n", rc_control.v_des[0]);
                    printf("RC_turn_rate:%.3f\n",  rc_control.omega_des[2]);
                    }

                    rc_control.v_des[1] = -1.0 * data.right_stick_y;// -v_scale * data.right_stick_y;
                    rc_control.v_des[2] = 0;

                    rc_control.omega_des[0] = 0;
                    rc_control.omega_des[1] = data.left_stick_x;//0;//pitch

                    rc_control.rpy_des[0] =roll_show;
                    rc_control.step_height=step_height;
                }
                else if(QP_Locomotion_switch==AT9S_BOOL_DOWN)
                {
                    selected_mode = RC_mode::QP_STAND;
                    rc_control.rpy_des[0] = data.left_stick_y;
                    rc_control.rpy_des[1] = data.left_stick_x;
                    rc_control.rpy_des[2] = data.right_stick_y;

                    rc_control.height_variation = data.right_stick_x;

                    rc_control.omega_des[0] = 0;
                    rc_control.omega_des[1] = 0;
                    rc_control.omega_des[2] = 0;
                }
            }
            else if(normal_jump_flip_switch==AT9S_TRI_UP)
            {
                selected_mode = RC_mode::BACKFLIP;
            }
            else if(normal_jump_flip_switch==AT9S_TRI_DOWN)
            {
                selected_mode = RC_mode::FRONT_JUMP;
            }

            break;

    }
    rc_control.mode = selected_mode;
}




void sbus_packet_complete() {
  Taranis_X7_data data;
  update_taranis_x7(&data);

  float v_scale = data.knobs[0]*1.5f + 2.0f; // from 0.5 to 3.5
  float w_scale = 2.*v_scale; // from 1.0 to 7.0
  //printf("v scale: %f\n", v_scale);

  auto estop_switch = data.right_lower_right_switch;
  auto mode_selection_switch = data.left_lower_left_switch;
  auto mode_go_switch = data.left_upper_switch;

  auto left_select = data.left_lower_right_switch;
  auto right_select = data.right_lower_left_switch;

  int selected_mode = 0;

  switch(estop_switch) {

    case SWITCH_UP: // ESTOP
      selected_mode = RC_mode::OFF;
      break;

    case SWITCH_MIDDLE: // recover
      selected_mode = RC_mode::RECOVERY_STAND;
      break;

    case SWITCH_DOWN: // run 
      selected_mode = RC_mode::LOCOMOTION; // locomotion by default

      // stand mode
      if(left_select == SWITCH_UP && right_select == SWITCH_UP) {
        selected_mode = RC_mode::QP_STAND;
      }

      if(backflip_prep_edge_trigger.trigger(mode_selection_switch) 
          && mode_selection_switch == SWITCH_MIDDLE) {
        initial_mode_go_switch = mode_go_switch;
      }

      // Experiment mode (two leg stance, vision, ...)
      if(experiment_prep_edge_trigger.trigger(mode_selection_switch) 
          && mode_selection_switch == SWITCH_DOWN) {
        initial_mode_go_switch = mode_go_switch;
      }


      // backflip
      if(mode_selection_switch == SWITCH_MIDDLE) {
        selected_mode = RC_mode::BACKFLIP_PRE;

        if(mode_go_switch == SWITCH_DOWN && initial_mode_go_switch != SWITCH_DOWN) {
          selected_mode = RC_mode::BACKFLIP;
        } else if(mode_go_switch == SWITCH_UP) {
          initial_mode_go_switch = SWITCH_UP;
        }
      } // Experiment Mode
      else if(mode_selection_switch == SWITCH_DOWN){
        int mode_id = left_select * 3 + right_select;

        if(mode_id == 0){ // Two leg stance
          selected_mode = RC_mode::TWO_LEG_STANCE_PRE;
          if(mode_go_switch == SWITCH_DOWN && initial_mode_go_switch != SWITCH_DOWN) {
            selected_mode = RC_mode::TWO_LEG_STANCE;
          } else if(mode_go_switch == SWITCH_UP) {
            initial_mode_go_switch = SWITCH_UP;
          }
        }
        else if(mode_id == 1){ // Vision 
          selected_mode = RC_mode::VISION;
        }
      }

      // gait selection
      int mode_id = left_select * 3 + right_select;

      constexpr int gait_table[9] = {0, //stand
        0, // trot
        1, // bounding
        2, // pronking
        3, // gallop
        5, // trot run
        6, // walk};
        7, // walk2?
        8, // pace
  };


  // Deadband
  for(int i(0); i<2; ++i){
    data.left_stick[i] = deadband(data.left_stick[i], 0.1, -1., 1.);
    data.right_stick[i] = deadband(data.right_stick[i], 0.1, -1., 1.);
  }

  if(selected_mode == RC_mode::LOCOMOTION || selected_mode == RC_mode::VISION) {
    rc_control.variable[0] = gait_table[mode_id];
    //rc_control.v_des[0] = v_scale * data.left_stick[1] * 0.5;
    //rc_control.v_des[1] = v_scale * data.left_stick[0] * -1.;
    rc_control.v_des[0] = v_scale * data.left_stick[1];
    rc_control.v_des[1] = -v_scale * data.left_stick[0];
    rc_control.v_des[2] = 0;

    rc_control.height_variation = data.knobs[1];
    //rc_control.p_des[2] = 0.27 + 0.08 * data.knobs[1]; // todo?

    rc_control.omega_des[0] = 0;
    rc_control.omega_des[1] = 0;
    rc_control.omega_des[2] = w_scale * data.right_stick[0];    //turn
    //rc_control.omega_des[2] = -v_scale * data.right_stick[0];

  } else if(selected_mode == RC_mode::QP_STAND || selected_mode == RC_mode::TWO_LEG_STANCE) {
    //rc_control.rpy_des[0] = data.left_stick[0] * 1.4;
    //rc_control.rpy_des[1] = data.right_stick[1] * 0.46;
    rc_control.rpy_des[0] = data.left_stick[0];
    rc_control.rpy_des[1] = data.right_stick[1];
    rc_control.rpy_des[2] = data.right_stick[0];

    rc_control.height_variation = data.left_stick[1];

    rc_control.omega_des[0] = 0;
    rc_control.omega_des[1] = 0;
    rc_control.omega_des[2] = 0;
    //rc_control.p_des[1] = -0.667 * rc_control.rpy_des[0];
    //rc_control.p_des[2] = data.left_stick[1] * .12;
  } 
  break;
}
//模式切换时候将模式给定到rc_control.mode上
bool trigger = mode_edge_trigger.trigger(selected_mode);
if(trigger || selected_mode == RC_mode::OFF || selected_mode == RC_mode::RECOVERY_STAND) {
  if(trigger) {
    printf("MODE TRIGGER!\n");
  }
  rc_control.mode = selected_mode;
}

}


void *v_memcpy(void *dest, volatile void *src, size_t n) {
  void *src_2 = (void *)src;
  return memcpy(dest, src_2, n);
}

float deadband(float command, float deadbandRegion, float minVal, float maxVal){
  if (command < deadbandRegion && command > -deadbandRegion) {
    return 0.0;
  } else {
    return (command / (2)) * (maxVal - minVal);
  }
}


