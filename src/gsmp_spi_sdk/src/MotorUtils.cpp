
/*!
 * <AUTHOR>
 * @date 2023-8-12
 * @email <EMAIL>
 * @brief Provide a template class of Motor as ethercat slaver
 */

#include "gsmp_spi_sdk/MotorUtils.h"

double gsmp::saturate(double value, double min_value, double max_value) {
    if (value >= max_value) {
        return max_value;
    } else if (value <= min_value) {
        return min_value;
    } else {
        return value;
    }
}

int gsmp::float_to_uint(float x, float x_min, float x_max, int bits) {
    /// Converts a float to an unsigned int, given range and number of bits ///
    float span = x_max - x_min;
    float offset = x_min;
    return (int) ((x - offset) * ((float) ((1 << bits) - 1)) / span);
}

float gsmp::uint_to_float(int x_int, float x_min, float x_max, int bits) {
    /// converts unsigned int to float, given range and number of bits ///
    float span = x_max - x_min;
    float offset = x_min;
    return ((float) x_int) * span / ((float) ((1 << bits) - 1)) + offset;
}