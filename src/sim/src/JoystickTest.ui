<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>JoystickTestWindow</class>
 <widget class="QDialog" name="JoystickTestWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>300</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Joystick Test</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QLabel" name="leftXLabel">
     <property name="text">
      <string>Left X:</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QLabel" name="leftYLabel">
     <property name="text">
      <string>Left Y:</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QLabel" name="rightXLabel">
     <property name="text">
      <string>Right X:</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QLabel" name="rightYLabel">
     <property name="text">
      <string>Right Y:</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Cancel|QDialogButtonBox::Ok</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>JoystickTestWindow</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>248</x>
     <y>254</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>JoystickTestWindow</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>316</x>
     <y>260</y>
    </hint>
    <hint type="destinationlabel">
     <x>286</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
