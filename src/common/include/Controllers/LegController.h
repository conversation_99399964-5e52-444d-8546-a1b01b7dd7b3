/*! @file LegController.h
 *  @brief Common Leg Control Interface and Leg Control Algorithms
 *
 *  Implements low-level leg control for Mini Cheetah and Cheetah 3 Robots
 *  Abstracts away the difference between the SPIne and the TI Boards (the low level leg control boards)
 *  All quantities are in the "leg frame" which has the same orientation as the
 * body frame, but is shifted so that 0,0,0 is at the ab/ad pivot (the "hip
 * frame").
 */

#ifndef PROJECT_LEGCONTROLLER_H
#define PROJECT_LEGCONTROLLER_H

#include "ros/ros.h"
#include <sensor_msgs/JointState.h>
#include <geometry_msgs/PoseArray.h>

#include "cppTypes.h"
#include "leg_control_command_lcmt.hpp"
#include "leg_control_data_lcmt.hpp"
#include "Dynamics/Quadruped.h"
#include "SimUtilities/SpineBoard.h"
#include "SimUtilities/ti_boardcontrol.h"
#include "gsmp_spi_sdk/spi-can.h"

#include <chrono>
#include <unistd.h>
#include <memory>
#include <thread>
#include <csignal>
using namespace std;

/*!
 * Data sent from the control algorithm to the legs.
 */
template <typename T>
struct LegControllerCommand {
  EIGEN_MAKE_ALIGNED_OPERATOR_NEW
  LegControllerCommand() { zero(); }

  void zero();

  Vec3<T> tauFeedForward, forceFeedForward, qDes, qdDes, pDes, vDes;
  Mat3<T> kpCartesian, kdCartesian, kpJoint, kdJoint;
};

struct JointData {
  std::array<float, 12> q; 
  std::array<float, 12> qd; 
  std::array<float, 12> tau;   
};

struct JointCommandData {
  std::array<float, 12> q_des;    // 目标关节角度
  std::array<float, 12> qd_des;   // 目标关节速度
  std::array<float, 12> tau_ff;   // 前馈力矩
};

/*!
 * Data returned from the legs to the control code.
 */
template <typename T>
struct LegControllerData {
  EIGEN_MAKE_ALIGNED_OPERATOR_NEW
  LegControllerData() { zero(); }

  void setQuadruped(Quadruped<T>& quad) { quadruped = &quad; }

  void zero();

  Vec3<T> q, qd, p, v,q_buffer,qd_buffer,qd_buffer_10[10],v_buffer,qd_numeric,qd_numeric_buffer;
  Mat3<T> J;
  Vec3<T> tauEstimate;
  Vec3<T> tauActuatual;
  Vec3<T> footforceActuatual;
  Vec3<T> footforceDesired;
  Quadruped<T>* quadruped;
};

/*!
 * Controller for 4 legs of a quadruped.  Works for both Mini Cheetah and Cheetah 3
 */
template <typename T>
class LegController {
 public:
  LegController(Quadruped<T>& quad) : _quadruped(quad) {
    for (auto& data : datas) data.setQuadruped(_quadruped);
  }

  void zeroCommand();
  void edampCommand(RobotType robot, T gain);
  #ifdef SIMULATION
  void updateData(const SpiData* spiData);
  void updateCommand(SpiCommand* spiCommand);
  void convertSpiToJointData(const SpiCommand* spiCmd, JointCommandData& out);
  #endif
  #ifdef REAL_ROBOT
  void updateData();
  void updateCommand();
  void convertJointDataToData(const gsmp::MotorState* motorstate, JointData& in);
  void convertSend12ToJointData(const gsmp::MotorCommand* sendCmd, JointCommandData& out);
  #endif
  void updateData(const TiBoardData* tiBoardData);
  void updateCommand(TiBoardCommand* tiBoardCommand);
  void setEnabled(bool enabled) { _legsEnabled = enabled; };
  void setLcm(leg_control_data_lcmt* data, leg_control_command_lcmt* command);
  void init(void);
  void initpub(void);
  void initSafetyCheck(void);
  void sendCommand12ToSlave(void);
  static void signalHandler(int signum);
  void emergencyStop(void);
  void publishJointStates(const JointData& jointData);
  void publishJointCommands(const JointCommandData& cmdData);

  /*!
   * Set the maximum torque.  This only works on cheetah 3!
   */
  void setMaxTorqueCheetah3(T tau) { _maxTorque = tau; }

  ros::NodeHandle nt;
  ros::Publisher  q_rel_pub,q_cmd_pub;

  LegControllerCommand<T> commands[4];
  LegControllerData<T> datas[4];
  Quadruped<T>& _quadruped;
  bool _legsEnabled = false;
  T _maxTorque = 0;
  bool _zeroEncoders = false;
  u32 _calibrateEncoders = 0;

  std::shared_ptr<gsmp::spiCan> spiCan_;
  bool emergency_flag{false};
  uint32_t transform_count = 0;
  
  gsmp::MotorState motorStates_[12];

  std::array<gsmp::MotorCommand, 12> sendCommand_salve1{};
  std::array<gsmp::MotorCommand, 12> sendCommand12_{};
    float transform_getDown_TargetPos[12] = {0.22, -1.06, 2.6,
                                           -0.22, -1.06, 2.6,
                                            0.22, -1.06, 2.6,
                                             -0.22, -1.06, 2.6};
  float transform_CurrentPos[12] = {-0.11, 1.07, -2.56, 0.11, 1.07, -2.56, -0.11, 1.07, -2.56, 0.11, 1.07, -2.56};
    // const vector<int> direction_motor{1, -1, -1, 1, 1, 1, -1, -1, -1, -1, 1, 1};
  const vector<int> direction_motor{-1, -1, -1,
                                    -1, 1, 1,
                                    1, -1, -1,
                                     1, 1, 1};
  
  // only used for actual robot mit
  const float abad_side_sign[4] = {-1.f, -1.f, 1.f, 1.f};
  const float hip_side_sign[4] = {-1.f, 1.f, -1.f, 1.f};
  const float knee_side_sign[4] = {-1.f, 1.f, -1.f, 1.f};
  
  // float base_motor[12] = {-0.323001155555556,	-1.34393308333333,	2.69275921111111,
  //                       -0.086239444444445,	0.914173083333333,	-2.77599581111111,
  //                       0.056582555555556,	-1.43732308333333,	2.57213861111111,
  //                       -0.457202555555556,	1.36530308333333,	-2.69878641111111
  //                       };
  //baigou
  float base_motor[12] = {
                          0.078610444444445,	-0.956823083333333,	3.00007161111111,
                          0.366259855555556,	1.01167308333333,	-2.84656761111111,
                          0.361834755555556,	-1.39215308333333,	2.76500951111111,
                          -0.008517555555556,	0.968643083333333,	-2.76523841111111
                          };

    
};

template <typename T>
void computeLegJacobianAndPosition(Quadruped<T>& quad, Vec3<T>& q, Mat3<T>* J,
                                   Vec3<T>* p, int leg);

#endif  // PROJECT_LEGCONTROLLER_H
